import { StyleSheet, Text, type TextProps } from 'react-native';

import { Typography } from '@/constants/DesignTokens';
import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link' | 'display' | 'h1' | 'h2' | 'h3' | 'h4' | 'bodyLarge' | 'bodySmall' | 'caption';
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...rest
}: ThemedTextProps) {
  const color = useThemeColor({ light: lightColor, dark: darkColor }, 'text');

  return (
    <Text
      style={[
        { color },
        type === 'default' ? styles.default : undefined,
        type === 'title' ? styles.title : undefined,
        type === 'defaultSemiBold' ? styles.defaultSemiBold : undefined,
        type === 'subtitle' ? styles.subtitle : undefined,
        type === 'link' ? styles.link : undefined,
        type === 'display' ? styles.display : undefined,
        type === 'h1' ? styles.h1 : undefined,
        type === 'h2' ? styles.h2 : undefined,
        type === 'h3' ? styles.h3 : undefined,
        type === 'h4' ? styles.h4 : undefined,
        type === 'bodyLarge' ? styles.bodyLarge : undefined,
        type === 'bodySmall' ? styles.bodySmall : undefined,
        type === 'caption' ? styles.caption : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  default: {
    fontSize: Typography.fontSize.body,
    lineHeight: Typography.lineHeight.body,
    fontWeight: Typography.fontWeight.regular,
    letterSpacing: Typography.letterSpacing.body,
  },
  defaultSemiBold: {
    fontSize: Typography.fontSize.body,
    lineHeight: Typography.lineHeight.body,
    fontWeight: Typography.fontWeight.bold,
    letterSpacing: Typography.letterSpacing.body,
  },
  title: {
    fontSize: Typography.fontSize.h1,
    lineHeight: Typography.lineHeight.h1,
    fontWeight: Typography.fontWeight.light,
    letterSpacing: Typography.letterSpacing.h1,
  },
  subtitle: {
    fontSize: Typography.fontSize.h3,
    lineHeight: Typography.lineHeight.h3,
    fontWeight: Typography.fontWeight.regular,
    letterSpacing: Typography.letterSpacing.h3,
  },
  link: {
    fontSize: Typography.fontSize.body,
    lineHeight: Typography.lineHeight.body,
    fontWeight: Typography.fontWeight.regular,
    letterSpacing: Typography.letterSpacing.body,
    color: '#0a7ea4',
  },
  display: {
    fontSize: Typography.fontSize.display,
    lineHeight: Typography.lineHeight.display,
    fontWeight: Typography.fontWeight.light,
    letterSpacing: Typography.letterSpacing.display,
  },
  h1: {
    fontSize: Typography.fontSize.h1,
    lineHeight: Typography.lineHeight.h1,
    fontWeight: Typography.fontWeight.light,
    letterSpacing: Typography.letterSpacing.h1,
  },
  h2: {
    fontSize: Typography.fontSize.h2,
    lineHeight: Typography.lineHeight.h2,
    fontWeight: Typography.fontWeight.regular,
    letterSpacing: Typography.letterSpacing.h2,
  },
  h3: {
    fontSize: Typography.fontSize.h3,
    lineHeight: Typography.lineHeight.h3,
    fontWeight: Typography.fontWeight.regular,
    letterSpacing: Typography.letterSpacing.h3,
  },
  h4: {
    fontSize: Typography.fontSize.h4,
    lineHeight: Typography.lineHeight.h4,
    fontWeight: Typography.fontWeight.medium,
    letterSpacing: Typography.letterSpacing.h4,
  },
  bodyLarge: {
    fontSize: Typography.fontSize.bodyLarge,
    lineHeight: Typography.lineHeight.bodyLarge,
    fontWeight: Typography.fontWeight.regular,
    letterSpacing: Typography.letterSpacing.body,
  },
  bodySmall: {
    fontSize: Typography.fontSize.bodySmall,
    lineHeight: Typography.lineHeight.bodySmall,
    fontWeight: Typography.fontWeight.regular,
    letterSpacing: Typography.letterSpacing.body,
  },
  caption: {
    fontSize: Typography.fontSize.caption,
    lineHeight: Typography.lineHeight.caption,
    fontWeight: Typography.fontWeight.regular,
    letterSpacing: Typography.letterSpacing.caption,
  },
});
