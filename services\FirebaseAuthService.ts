import {
  ConfirmationResult,
  onAuthStateChanged,
  RecaptchaVerifier,
  signInWithPhoneNumber,
  signOut,
  User
} from 'firebase/auth';
import { doc, getDoc, serverTimestamp, setDoc, updateDoc } from 'firebase/firestore';
import { Platform } from 'react-native';
import { auth, db } from './FirebaseService';

export interface UserProfile {
  uid: string;
  email?: string;
  displayName?: string;
  phoneNumber?: string;
  photoURL?: string;
  username?: string;
  primaryLanguage: string;
  secondaryLanguages: string[];
  autoTranslate: boolean;
  voiceEnabled: boolean;
  signLanguageEnabled: boolean;
  fontSize: 'small' | 'medium' | 'large';
  status: 'online' | 'away' | 'busy' | 'offline';
  statusMessage?: string;
  lastSeen: any;
  createdAt: any;
  updatedAt: any;
}

export class FirebaseAuthService {
  private static currentUser: User | null = null;
  private static authStateListeners: ((user: User | null) => void)[] = [];

  static initialize() {
    onAuthStateChanged(auth, (user) => {
      this.currentUser = user;
      this.authStateListeners.forEach(listener => listener(user));
      
      if (user) {
        // Update user status to online
        this.updateUserStatus('online');
      }
    });
  }

  static onAuthStateChanged(callback: (user: User | null) => void) {
    this.authStateListeners.push(callback);
    // Call immediately with current state
    callback(this.currentUser);
    
    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(callback);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  static getCurrentUser(): User | null {
    return this.currentUser;
  }



  static async signInWithPhone(phoneNumber: string): Promise<ConfirmationResult> {
    try {
      // Note: Phone auth requires additional setup for React Native
      // This is a simplified version - you'll need to implement proper phone auth
      const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, new RecaptchaVerifier('recaptcha-container', {}, auth));
      return confirmationResult;
    } catch (error) {
      console.error('Phone sign in error:', error);
      throw error;
    }
  }

  static async verifyPhoneCode(confirmationResult: ConfirmationResult, code: string): Promise<User> {
    try {
      const userCredential = await confirmationResult.confirm(code);
      const user = userCredential.user;

      // Check if user profile exists, create if not
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      if (!userDoc.exists()) {
        await this.createUserProfile(user);
      }

      return user;
    } catch (error) {
      console.error('Phone verification error:', error);
      throw error;
    }
  }



  static async signInWithPhoneNumber(phoneNumber: string): Promise<ConfirmationResult> {
    try {
      if (Platform.OS === 'web') {
        // Ensure a reCAPTCHA container exists in the DOM
        let container = document.getElementById('recaptcha-container');
        if (!container) {
          container = document.createElement('div');
          container.id = 'recaptcha-container';
          // Attach to body to avoid layout issues inside RNW views
          document.body.appendChild(container);
        } else {
          container.innerHTML = '';
        }

        // Create reCAPTCHA verifier for web with proper constructor (auth, container, params)
        const appVerifier = new RecaptchaVerifier(auth, container, {
          size: 'normal',
          callback: () => {
            console.log('reCAPTCHA verification successful');
          },
          'expired-callback': () => {
            console.log('reCAPTCHA expired');
          },
          'error-callback': (error: any) => {
            console.error('reCAPTCHA error:', error);
          }
        });

        // Render the reCAPTCHA and wait for it
        await appVerifier.render();

        // Send SMS with phone number verification
        return await signInWithPhoneNumber(auth, phoneNumber, appVerifier);
      } else {
        // Mobile implementation - try native first, fallback to web
        try {
          const rnAuthModule = await import('@react-native-firebase/auth');
          const rnAuth = rnAuthModule.default; // default export is the auth() function
          return await rnAuth().signInWithPhoneNumber(phoneNumber);
        } catch (nativeErr: any) {
          console.warn('Native phone auth not available, falling back to web implementation:', nativeErr);

          // Fallback to web implementation for Expo Go
          // For mobile, we need to ensure the reCAPTCHA container exists
          if (Platform.OS !== 'web') {
            // On mobile, we can't use reCAPTCHA, so we throw a more helpful error
            throw new Error('Phone authentication requires a development build with native Firebase modules. Please use "expo run:android" or build with EAS.');
          }

          // This should not be reached on mobile, but keeping as fallback
          const appVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
            size: 'invisible',
            callback: () => {
              console.log('reCAPTCHA verification successful');
            }
          });

          return await signInWithPhoneNumber(auth, phoneNumber, appVerifier);
        }
      }
    } catch (error: any) {
      console.error('Phone auth error:', error);

      // Handle specific Firebase errors
      if (error.code === 'auth/invalid-phone-number') {
        throw new Error('Invalid phone number. Please use international format (+1234567890)');
      } else if (error.code === 'auth/too-many-requests') {
        throw new Error('Too many requests. Please wait before trying again.');
      } else if (error.code === 'auth/invalid-app-credential') {
        throw new Error('App not configured for phone authentication. Check Firebase Console.');
      } else if (error.code === 'auth/quota-exceeded') {
        throw new Error('SMS quota exceeded. Please try again later.');
      } else if (error.code === 'auth/app-not-authorized') {
        throw new Error('Domain not authorized. Add your domain to Firebase Console authorized domains.');
      } else if (error.code === 'auth/captcha-check-failed') {
        throw new Error('reCAPTCHA verification failed. Please complete the reCAPTCHA.');
      } else if (error.code === 'auth/missing-app-credential') {
        throw new Error('Firebase app credentials missing. Check your configuration.');
      }

      throw error;
    }
  }

  static async signOut(): Promise<void> {
    try {
      // Update status to offline before signing out
      if (this.currentUser) {
        await this.updateUserStatus('offline');
      }
      await signOut(auth);
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }



  static async createUserProfile(user: User, additionalData?: Partial<UserProfile>): Promise<void> {
    try {
      const userProfile: UserProfile = {
        uid: user.uid,
        email: user.email || undefined,
        displayName: user.displayName || additionalData?.displayName || '',
        phoneNumber: user.phoneNumber || undefined,
        photoURL: user.photoURL || undefined,
        username: additionalData?.username || user.displayName?.toLowerCase().replace(/\s+/g, '') || '',
        primaryLanguage: 'en',
        secondaryLanguages: [],
        autoTranslate: true,
        voiceEnabled: true,
        signLanguageEnabled: false,
        fontSize: 'medium',
        status: 'online',
        statusMessage: '',
        lastSeen: serverTimestamp(),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        ...additionalData
      };

      await setDoc(doc(db, 'users', user.uid), userProfile);
    } catch (error) {
      console.error('Create user profile error:', error);
      throw error;
    }
  }

  static async getUserProfile(uid: string): Promise<UserProfile | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid));
      if (userDoc.exists()) {
        return userDoc.data() as UserProfile;
      }
      return null;
    } catch (error) {
      console.error('Get user profile error:', error);
      return null;
    }
  }

  static async updateUserProfile(uid: string, updates: Partial<UserProfile>): Promise<void> {
    try {
      await updateDoc(doc(db, 'users', uid), {
        ...updates,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Update user profile error:', error);
      throw error;
    }
  }

  static async updateUserStatus(status: 'online' | 'away' | 'busy' | 'offline', statusMessage?: string): Promise<void> {
    try {
      if (!this.currentUser) return;

      await updateDoc(doc(db, 'users', this.currentUser.uid), {
        status,
        statusMessage: statusMessage || '',
        lastSeen: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Update user status error:', error);
    }
  }

  static async searchUsers(query: string, limit: number = 20): Promise<UserProfile[]> {
    try {
      // Note: Firestore doesn't support full-text search natively
      // You might want to use Algolia or implement a different search strategy
      // This is a simplified version
      const { collection, getDocs, query: firestoreQuery, where, orderBy, limitToLast } = await import('firebase/firestore');
      
      const usersRef = collection(db, 'users');
      const q = firestoreQuery(
        usersRef,
        where('username', '>=', query.toLowerCase()),
        where('username', '<=', query.toLowerCase() + '\uf8ff'),
        orderBy('username'),
        limitToLast(limit)
      );

      const querySnapshot = await getDocs(q);
      const users: UserProfile[] = [];
      
      querySnapshot.forEach((doc) => {
        users.push(doc.data() as UserProfile);
      });

      return users;
    } catch (error) {
      console.error('Search users error:', error);
      return [];
    }
  }
}
