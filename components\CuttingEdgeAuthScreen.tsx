import React, { useEffect, useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';


import CuttingEdgeButton from '@/components/CuttingEdgeButton';
import CuttingEdgeInput from '@/components/CuttingEdgeInput';
import ThemeToggle from '@/components/MoveableThemeToggle';
import { ThemedText } from '@/components/ThemedText';
import ThemeTransition from '@/components/ThemeTransition';
import { Spacing } from '@/constants/DesignTokens';
import { useFuturisticTheme } from '@/hooks/useFuturisticTheme';
import { FirebaseAuthService } from '@/services/FirebaseAuthService';

interface AuthScreenProps {
  onLogin: () => void;
}

export default function CuttingEdgeAuthScreen({ onLogin }: AuthScreenProps) {
  const { colors, theme, toggleTheme } = useFuturisticTheme();
  const [authMode, setAuthMode] = useState<'signin' | 'signup' | 'reset'>('signin');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    FirebaseAuthService.initialize();

    const unsubscribe = FirebaseAuthService.onAuthStateChanged((user) => {
      if (user) {
        onLogin();
      }
    });

    return unsubscribe;
  }, [onLogin]);



  const handleSignIn = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    setLoading(true);
    try {
      await FirebaseAuthService.signInWithEmail(email, password);
      // onLogin will be called automatically via auth state change
    } catch (error: any) {
      console.error('Sign in error:', error);
      Alert.alert('Error', error.message || 'Failed to sign in');
    } finally {
      setLoading(false);
    }
  };

  const handleSignUp = async () => {
    if (!email || !password || !displayName) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    // Validate password strength
    if (password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters long');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    setLoading(true);
    try {
      await FirebaseAuthService.signUpWithEmail(email, password, displayName);
      // onLogin will be called automatically via auth state change
    } catch (error: any) {
      console.error('Sign up error:', error);
      Alert.alert('Error', error.message || 'Failed to create account');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordReset = async () => {
    if (!email) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    setLoading(true);
    try {
      await FirebaseAuthService.resetPassword(email);
      Alert.alert('Success', 'Password reset email sent. Check your inbox.');
      setAuthMode('signin');
    } catch (error: any) {
      console.error('Password reset error:', error);
      Alert.alert('Error', error.message || 'Failed to send reset email');
    } finally {
      setLoading(false);
    }
  };





  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ThemeToggle onToggle={toggleTheme} />
      <ThemeTransition>
        <KeyboardAvoidingView
          style={styles.keyboardContainer}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
          >
            <View style={styles.header}>
              <ThemedText type="h1" style={[styles.title, { color: colors.text }]}>
                voXchat
              </ThemedText>
              <ThemedText type="bodySmall" style={[styles.subtitle, { color: colors.textSecondary }]}>
                Connect across languages and abilities
              </ThemedText>
            </View>

            <View style={styles.form}>

              <View style={styles.inputContainer}>
                {/* Email Input - Always visible */}
                <CuttingEdgeInput
                  placeholder="Email Address"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  variant="minimal"
                  style={styles.input}
                />

                {/* Password Input - Visible for signin and signup */}
                {(authMode === 'signin' || authMode === 'signup') && (
                  <CuttingEdgeInput
                    placeholder="Password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry
                    variant="minimal"
                    style={styles.input}
                  />
                )}

                {/* Display Name Input - Only for signup */}
                {authMode === 'signup' && (
                  <CuttingEdgeInput
                    placeholder="Display Name"
                    value={displayName}
                    onChangeText={setDisplayName}
                    variant="minimal"
                    style={styles.input}
                  />
                )}

                {/* Confirm Password Input - Only for signup */}
                {authMode === 'signup' && (
                  <CuttingEdgeInput
                    placeholder="Confirm Password"
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    secureTextEntry
                    variant="minimal"
                    style={styles.input}
                  />
                )}
              </View>

              {/* Primary Action Button */}
              {authMode === 'signin' && (
                <CuttingEdgeButton
                  title={loading ? 'Signing In...' : 'Sign In'}
                  onPress={handleSignIn}
                  variant="primary"
                  size="large"
                  disabled={loading}
                  loading={loading}
                  style={styles.authButton}
                />
              )}

              {authMode === 'signup' && (
                <CuttingEdgeButton
                  title={loading ? 'Creating Account...' : 'Create Account'}
                  onPress={handleSignUp}
                  variant="primary"
                  size="large"
                  disabled={loading}
                  loading={loading}
                  style={styles.authButton}
                />
              )}

              {authMode === 'reset' && (
                <CuttingEdgeButton
                  title={loading ? 'Sending Reset Email...' : 'Send Reset Email'}
                  onPress={handlePasswordReset}
                  variant="primary"
                  size="large"
                  disabled={loading}
                  loading={loading}
                  style={styles.authButton}
                />
              )}

              {/* Mode Switch Buttons */}
              {authMode === 'signin' && (
                <View style={styles.switchContainer}>
                  <TouchableOpacity onPress={() => setAuthMode('signup')} style={styles.switchButton}>
                    <ThemedText type="bodySmall" style={[styles.switchText, { color: colors.textSecondary }]}>
                      Don't have an account? Sign up
                    </ThemedText>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => setAuthMode('reset')} style={styles.switchButton}>
                    <ThemedText type="bodySmall" style={[styles.switchText, { color: colors.textSecondary }]}>
                      Forgot password?
                    </ThemedText>
                  </TouchableOpacity>
                </View>
              )}

              {authMode === 'signup' && (
                <TouchableOpacity onPress={() => setAuthMode('signin')} style={styles.switchButton}>
                  <ThemedText type="bodySmall" style={[styles.switchText, { color: colors.textSecondary }]}>
                    Already have an account? Sign in
                  </ThemedText>
                </TouchableOpacity>
              )}

              {authMode === 'reset' && (
                <TouchableOpacity onPress={() => setAuthMode('signin')} style={styles.switchButton}>
                  <ThemedText type="bodySmall" style={[styles.switchText, { color: colors.textSecondary }]}>
                    Back to sign in
                  </ThemedText>
                </TouchableOpacity>
              )}

            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </ThemeTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingVertical: Spacing.xl,
    paddingHorizontal: 0,
  },
  header: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
    paddingHorizontal: Spacing.lg,
  },
  title: {
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  subtitle: {
    textAlign: 'center',
    opacity: 0.7,
  },
  form: {
    width: '100%',
    maxWidth: 380,
    alignSelf: 'center',
    paddingHorizontal: Spacing.lg,
  },
  inputContainer: {
    gap: Spacing.sm,
    marginBottom: Spacing.lg,
  },
  input: {
    marginBottom: Spacing.sm,
  },
  authButton: {
    marginBottom: Spacing.lg,
  },
  authMethodsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: Spacing.lg,
    marginTop: Spacing.sm,
  },
  authMethodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  iconEmoji: {
    fontSize: 20,
  },

  switchContainer: {
    gap: Spacing.xs,
    marginTop: Spacing.sm,
  },
  switchButton: {
    alignItems: 'center',
    paddingVertical: Spacing.sm,
  },
  switchText: {
    textAlign: 'center',
  },
});
