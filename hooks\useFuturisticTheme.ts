import { FuturisticThemes, ThemeColors } from '@/constants/FuturisticThemes';
import { StorageService } from '@/services/StorageService';
import { useEffect, useRef, useState } from 'react';
import { Animated, Easing } from 'react-native';

export type ThemeMode = 'white' | 'superBlack';

export function useFuturisticTheme() {
  const [theme, setTheme] = useState<ThemeMode>('white');
  const [isTransitioning, setIsTransitioning] = useState(false);
  const transitionAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    loadTheme();
  }, []);

  const loadTheme = async () => {
    try {
      const savedTheme = await StorageService.getTheme();
      setTheme(savedTheme as ThemeMode);
    } catch (error) {
      console.log('Error loading theme:', error);
    }
  };

  const animateThemeChange = (newTheme: ThemeMode) => {
    setIsTransitioning(true);

    Animated.sequence([
      Animated.timing(transitionAnim, {
        toValue: 0,
        duration: 150,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
      Animated.timing(transitionAnim, {
        toValue: 1,
        duration: 200,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsTransitioning(false);
    });

    // Change theme in the middle of animation
    setTimeout(() => {
      setTheme(newTheme);
    }, 150);
  };

  const toggleTheme = async () => {
    const newTheme = theme === 'white' ? 'superBlack' : 'white';
    animateThemeChange(newTheme);
    await StorageService.setTheme(newTheme);
  };

  const setThemeMode = async (newTheme: ThemeMode) => {
    if (newTheme !== theme) {
      animateThemeChange(newTheme);
      await StorageService.setTheme(newTheme);
    }
  };

  return {
    theme,
    colors: FuturisticThemes[theme] as ThemeColors,
    toggleTheme,
    setTheme: setThemeMode,
    isLight: theme === 'white',
    isDark: theme === 'superBlack',
    isTransitioning,
    transitionOpacity: transitionAnim,
  };
}