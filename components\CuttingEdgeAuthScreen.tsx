import React, { useEffect, useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import CountrySelector from '@/components/CountrySelector';
import CuttingEdgeButton from '@/components/CuttingEdgeButton';
import CuttingEdgeInput from '@/components/CuttingEdgeInput';
import ThemeToggle from '@/components/MoveableThemeToggle';
import { ThemedText } from '@/components/ThemedText';
import ThemeTransition from '@/components/ThemeTransition';
import { Country, detectCountryFromTimezone } from '@/constants/CountryCodes';
import { Spacing } from '@/constants/DesignTokens';
import { useFuturisticTheme } from '@/hooks/useFuturisticTheme';
import { FirebaseAuthService } from '@/services/FirebaseAuthService';

interface AuthScreenProps {
  onLogin: () => void;
}

export default function CuttingEdgeAuthScreen({ onLogin }: AuthScreenProps) {
  const { colors, theme, toggleTheme } = useFuturisticTheme();
  const [authMode, setAuthMode] = useState<'phone' | 'otp'>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [selectedCountry, setSelectedCountry] = useState<Country>(detectCountryFromTimezone());
  const [otpCode, setOtpCode] = useState('');
  const [confirmationResult, setConfirmationResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    FirebaseAuthService.initialize();

    const unsubscribe = FirebaseAuthService.onAuthStateChanged((user) => {
      if (user) {
        onLogin();
      }
    });

    return unsubscribe;
  }, [onLogin]);



  const handlePhoneAuth = async () => {
    if (!phoneNumber) {
      Alert.alert('Error', 'Please enter your phone number');
      return;
    }

    // Validate phone number format
    const phoneRegex = /^[0-9]{6,15}$/;
    if (!phoneRegex.test(phoneNumber)) {
      Alert.alert('Error', 'Please enter a valid phone number (6-15 digits)');
      return;
    }

    const fullPhoneNumber = `${selectedCountry.dialCode}${phoneNumber}`;
    setLoading(true);
    try {
      const confirmation = await FirebaseAuthService.signInWithPhoneNumber(fullPhoneNumber);
      setConfirmationResult(confirmation);
      setAuthMode('otp');
      Alert.alert('Success', `OTP sent to ${fullPhoneNumber}. Check your messages.`);
    } catch (error: any) {
      console.error('Phone auth error:', error);
      let errorMessage = 'Failed to send OTP';
      
      if (error.code === 'auth/invalid-phone-number') {
        errorMessage = 'Invalid phone number format. Use international format.';
      } else if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Too many requests. Please try again later.';
      } else if (error.code === 'auth/quota-exceeded') {
        errorMessage = 'SMS quota exceeded. Please try again later.';
      } else if (error.code === 'auth/invalid-app-credential') {
        errorMessage = 'Phone authentication not configured. Check Firebase Console.';
      } else if (error.code === 'auth/app-not-authorized') {
        errorMessage = 'App not authorized. Add SHA-1 fingerprint to Firebase Console.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleOtpVerification = async () => {
    if (!otpCode || !confirmationResult) {
      Alert.alert('Error', 'Please enter the OTP code');
      return;
    }

    setLoading(true);
    try {
      await FirebaseAuthService.verifyPhoneCode(confirmationResult, otpCode);
      // onLogin will be called automatically via auth state change
    } catch (error: any) {
      console.error('OTP verification error:', error);
      Alert.alert('Error', error.message || 'Invalid OTP code');
    } finally {
      setLoading(false);
    }
  };





  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ThemeToggle onToggle={toggleTheme} />
      <ThemeTransition>
        <KeyboardAvoidingView
          style={styles.keyboardContainer}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
          >
            <View style={styles.header}>
              <ThemedText type="h1" style={[styles.title, { color: colors.text }]}>
                voXchat
              </ThemedText>
              <ThemedText type="bodySmall" style={[styles.subtitle, { color: colors.textSecondary }]}>
                Connect across languages and abilities
              </ThemedText>
            </View>

            <View style={styles.form}>

              <View style={styles.inputContainer}>
                {authMode === 'phone' && (
                  <View style={styles.phoneInputContainer}>
                    <CountrySelector
                      selectedCountry={selectedCountry}
                      onCountrySelect={setSelectedCountry}
                    />
                    <CuttingEdgeInput
                      placeholder="Phone Number"
                      value={phoneNumber}
                      onChangeText={setPhoneNumber}
                      keyboardType="phone-pad"
                      variant="minimal"
                      style={styles.phoneInput}
                    />
                  </View>
                )}

                {authMode === 'otp' && (
                  <CuttingEdgeInput
                    placeholder="Enter OTP Code"
                    value={otpCode}
                    onChangeText={setOtpCode}
                    keyboardType="number-pad"
                    variant="minimal"
                    maxLength={6}
                  />
                )}
              </View>

              {/* Primary Action Button */}
              {authMode === 'phone' && (
                <CuttingEdgeButton
                  title={loading ? 'Sending OTP...' : 'Send OTP'}
                  onPress={handlePhoneAuth}
                  variant="primary"
                  size="large"
                  disabled={loading}
                  loading={loading}
                  style={styles.authButton}
                />
              )}

              {authMode === 'otp' && (
                <CuttingEdgeButton
                  title={loading ? 'Verifying...' : 'Verify OTP'}
                  onPress={handleOtpVerification}
                  variant="primary"
                  size="large"
                  disabled={loading}
                  loading={loading}
                  style={styles.authButton}
                />
              )}

              {/* Back to Phone Input */}
              {authMode === 'otp' && (
                <TouchableOpacity onPress={() => setAuthMode('phone')} style={styles.switchButton}>
                  <ThemedText type="bodySmall" style={[styles.switchText, { color: colors.textSecondary }]}>
                    Change phone number
                  </ThemedText>
                </TouchableOpacity>
              )}
              
              {/* reCAPTCHA container for web phone auth */}
              {Platform.OS === 'web' && (
                <View style={styles.recaptchaContainer}>
                  <div id="recaptcha-container" />
                </View>
              )}
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </ThemeTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingVertical: Spacing.xl,
    paddingHorizontal: 0,
  },
  header: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
    paddingHorizontal: Spacing.lg,
  },
  title: {
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  subtitle: {
    textAlign: 'center',
    opacity: 0.7,
  },
  form: {
    width: '100%',
    maxWidth: 380,
    alignSelf: 'center',
    paddingHorizontal: Spacing.lg,
  },
  inputContainer: {
    gap: Spacing.sm,
    marginBottom: Spacing.lg,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    gap: Spacing.sm,
    alignItems: 'center',
  },
  phoneInput: {
    flex: 1,
  },
  authButton: {
    marginBottom: Spacing.lg,
  },
  authMethodsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: Spacing.lg,
    marginTop: Spacing.sm,
  },
  authMethodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  iconEmoji: {
    fontSize: 20,
  },

  switchButton: {
    alignItems: 'center',
    paddingVertical: Spacing.sm,
  },
  switchText: {
    textAlign: 'center',
  },
  forgotButton: {
    alignItems: 'center',
    paddingVertical: Spacing.xs,
    marginTop: Spacing.sm,
  },
  forgotText: {
    textAlign: 'center',
  },
  recaptchaContainer: {
    marginVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 78, // Standard reCAPTCHA height
  },
});
