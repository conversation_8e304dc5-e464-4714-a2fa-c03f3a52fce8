import FuturisticCard from '@/components/FuturisticCard';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useFuturisticTheme } from '@/hooks/useFuturisticTheme';
import { FirebaseAuthService, UserProfile } from '@/services/FirebaseAuthService';
import { Conversation, FirebaseChatService } from '@/services/FirebaseChatService';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, FlatList, RefreshControl, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function ChatsScreen() {
  const { colors } = useFuturisticTheme();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentUser, setCurrentUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    initializeChats();
  }, []);

  const initializeChats = async () => {
    try {
      const user = FirebaseAuthService.getCurrentUser();
      if (!user) {
        router.replace('/');
        return;
      }

      // Get user profile
      const userProfile = await FirebaseAuthService.getUserProfile(user.uid);
      setCurrentUser(userProfile);

      // Subscribe to conversations
      const unsubscribe = FirebaseChatService.subscribeToUserConversations(
        user.uid,
        (userConversations) => {
          setConversations(userConversations);
          setLoading(false);
          setRefreshing(false);
        }
      );

      return unsubscribe;
    } catch (error) {
      console.error('Error initializing chats:', error);
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    initializeChats();
  };

  const startNewChat = async () => {
    Alert.prompt(
      'New Chat',
      'Enter the email of the person you want to chat with:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Start Chat',
          onPress: async (email) => {
            if (!email || !currentUser) return;

            try {
              // Search for user by email
              const users = await FirebaseAuthService.searchUsers(email.toLowerCase());
              const targetUser = users.find(u => u.email?.toLowerCase() === email.toLowerCase());

              if (!targetUser) {
                Alert.alert('Error', 'User not found with that email address');
                return;
              }

              if (targetUser.uid === currentUser.uid) {
                Alert.alert('Error', 'You cannot start a chat with yourself');
                return;
              }

              // Create or get existing conversation
              const conversationId = await FirebaseChatService.createDirectConversation(targetUser.uid);
              router.push(`/chat/${conversationId}`);
            } catch (error) {
              console.error('Error starting new chat:', error);
              Alert.alert('Error', 'Failed to start new chat');
            }
          }
        }
      ],
      'plain-text',
      '',
      'email-address'
    );
  };

  const openChat = (conversation: Conversation) => {
    router.push(`/chat/${conversation.id}`);
  };

  const formatTime = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);

    if (diffHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString();
    }
  };

  const getConversationTitle = (conversation: Conversation) => {
    if (conversation.type === 'group') {
      return conversation.name || 'Group Chat';
    }

    // For direct chats, show the other participant's name
    const otherParticipant = conversation.participants.find(p => p !== currentUser?.uid);
    return otherParticipant || 'Direct Chat';
  };

  const getUnreadCount = (conversation: Conversation) => {
    if (!currentUser) return 0;
    return conversation.unreadCounts?.[currentUser.uid] || 0;
  };

  const renderConversation = ({ item }: { item: Conversation }) => {
    const unreadCount = getUnreadCount(item);
    const hasUnread = unreadCount > 0;

    return (
      <TouchableOpacity onPress={() => openChat(item)}>
        <FuturisticCard variant="glass" style={styles.conversationCard}>
          <View style={styles.conversationHeader}>
            <View style={styles.conversationInfo}>
              <ThemedText
                type="defaultSemiBold"
                style={[
                  styles.conversationTitle,
                  { color: colors.text },
                  hasUnread && { fontWeight: 'bold' }
                ]}
              >
                {getConversationTitle(item)}
              </ThemedText>

              {item.lastMessage && (
                <ThemedText
                  style={[
                    styles.lastMessage,
                    { color: colors.textSecondary },
                    hasUnread && { color: colors.text, fontWeight: '500' }
                  ]}
                  numberOfLines={1}
                >
                  {item.lastMessage}
                </ThemedText>
              )}
            </View>

            <View style={styles.conversationMeta}>
              {item.lastMessageTime && (
                <ThemedText style={[styles.timeText, { color: colors.textSecondary }]}>
                  {formatTime(item.lastMessageTime)}
                </ThemedText>
              )}

              {hasUnread && (
                <View style={[styles.unreadBadge, { backgroundColor: colors.primary }]}>
                  <ThemedText style={styles.unreadText}>
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </ThemedText>
                </View>
              )}
            </View>
          </View>

          {item.typingUsers && item.typingUsers.length > 0 && (
            <View style={styles.typingContainer}>
              <ThemedText style={[styles.typingText, { color: colors.primary }]}>
                Someone is typing...
              </ThemedText>
            </View>
          )}
        </FuturisticCard>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <IconSymbol name="message" size={64} color={colors.textSecondary} />
      <ThemedText style={[styles.emptyTitle, { color: colors.text }]}>
        No conversations yet
      </ThemedText>
      <ThemedText style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
        Start a new chat to begin messaging
      </ThemedText>
      <TouchableOpacity
        style={[styles.startChatButton, { backgroundColor: colors.primary }]}
        onPress={startNewChat}
      >
        <ThemedText style={styles.startChatButtonText}>
          Start New Chat
        </ThemedText>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: colors.background }]}>
        <ThemedText style={{ color: colors.text }}>Loading conversations...</ThemedText>
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <ThemedText type="title" style={{ color: colors.text }}>
          Chats
        </ThemedText>
        <TouchableOpacity onPress={startNewChat}>
          <IconSymbol name="plus" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <FlatList
        data={conversations}
        renderItem={renderConversation}
        keyExtractor={(item) => item.id}
        contentContainerStyle={[
          styles.listContent,
          conversations.length === 0 && styles.emptyListContent
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={colors.primary}
          />
        }
        ListEmptyComponent={renderEmptyState}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  listContent: {
    padding: 16,
  },
  emptyListContent: {
    flexGrow: 1,
  },
  conversationCard: {
    marginBottom: 12,
    padding: 16,
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  conversationInfo: {
    flex: 1,
    marginRight: 12,
  },
  conversationTitle: {
    fontSize: 16,
    marginBottom: 4,
  },
  lastMessage: {
    fontSize: 14,
    lineHeight: 18,
  },
  conversationMeta: {
    alignItems: 'flex-end',
  },
  timeText: {
    fontSize: 12,
    marginBottom: 4,
  },
  unreadBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  typingContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  typingText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  startChatButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  startChatButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
