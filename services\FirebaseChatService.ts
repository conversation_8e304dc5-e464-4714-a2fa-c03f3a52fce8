import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  Timestamp,
  writeBatch,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from './FirebaseService';
import { FirebaseAuthService } from './FirebaseAuthService';

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  content: string;
  messageType: 'text' | 'voice' | 'image' | 'video' | 'file';
  originalLanguage?: string;
  translatedContent?: { [languageCode: string]: string };
  mediaUrl?: string;
  mediaDuration?: number; // for voice/video messages
  mediaSize?: number;
  replyToMessageId?: string;
  reactions?: { [emoji: string]: string[] }; // emoji -> array of user IDs
  readBy: string[]; // array of user IDs who have read the message
  deliveredTo: string[]; // array of user IDs who have received the message
  createdAt: Timestamp;
  updatedAt: Timestamp;
  deletedAt?: Timestamp;
}

export interface Conversation {
  id: string;
  type: 'direct' | 'group';
  name?: string; // for group chats
  description?: string;
  participants: string[]; // array of user IDs
  admins: string[]; // array of admin user IDs
  lastMessage?: string;
  lastMessageTime?: Timestamp;
  lastMessageSender?: string;
  unreadCounts: { [userId: string]: number };
  typingUsers: string[]; // array of user IDs currently typing
  createdBy: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  isActive: boolean;
}

export interface VoiceMessage {
  messageId: string;
  audioUrl: string;
  duration: number;
  waveformData?: number[];
  transcription?: string;
  transcriptionLanguage?: string;
}

export class FirebaseChatService {
  // Conversation methods
  static async createDirectConversation(otherUserId: string): Promise<string> {
    try {
      const currentUser = FirebaseAuthService.getCurrentUser();
      if (!currentUser) throw new Error('User not authenticated');

      // Check if conversation already exists
      const existingConversation = await this.findDirectConversation(currentUser.uid, otherUserId);
      if (existingConversation) {
        return existingConversation.id;
      }

      // Create new conversation
      const conversationData: Omit<Conversation, 'id'> = {
        type: 'direct',
        participants: [currentUser.uid, otherUserId],
        admins: [currentUser.uid],
        unreadCounts: {
          [currentUser.uid]: 0,
          [otherUserId]: 0
        },
        typingUsers: [],
        createdBy: currentUser.uid,
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
        isActive: true
      };

      const docRef = await addDoc(collection(db, 'conversations'), conversationData);
      return docRef.id;
    } catch (error) {
      console.error('Create direct conversation error:', error);
      throw error;
    }
  }

  static async createGroupConversation(
    name: string,
    participantIds: string[],
    description?: string
  ): Promise<string> {
    try {
      const currentUser = FirebaseAuthService.getCurrentUser();
      if (!currentUser) throw new Error('User not authenticated');

      const allParticipants = [currentUser.uid, ...participantIds];
      const unreadCounts: { [userId: string]: number } = {};
      allParticipants.forEach(userId => {
        unreadCounts[userId] = 0;
      });

      const conversationData: Omit<Conversation, 'id'> = {
        type: 'group',
        name,
        description,
        participants: allParticipants,
        admins: [currentUser.uid],
        unreadCounts,
        typingUsers: [],
        createdBy: currentUser.uid,
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
        isActive: true
      };

      const docRef = await addDoc(collection(db, 'conversations'), conversationData);
      return docRef.id;
    } catch (error) {
      console.error('Create group conversation error:', error);
      throw error;
    }
  }

  static async findDirectConversation(userId1: string, userId2: string): Promise<Conversation | null> {
    try {
      const conversationsRef = collection(db, 'conversations');
      const q = query(
        conversationsRef,
        where('type', '==', 'direct'),
        where('participants', 'array-contains', userId1),
        where('isActive', '==', true)
      );

      const querySnapshot = await getDocs(q);
      
      for (const docSnapshot of querySnapshot.docs) {
        const conversation = { id: docSnapshot.id, ...docSnapshot.data() } as Conversation;
        if (conversation.participants.includes(userId2)) {
          return conversation;
        }
      }

      return null;
    } catch (error) {
      console.error('Find direct conversation error:', error);
      return null;
    }
  }

  static async getUserConversations(userId: string): Promise<Conversation[]> {
    try {
      const conversationsRef = collection(db, 'conversations');
      const q = query(
        conversationsRef,
        where('participants', 'array-contains', userId),
        where('isActive', '==', true),
        orderBy('updatedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const conversations: Conversation[] = [];

      querySnapshot.forEach((doc) => {
        conversations.push({ id: doc.id, ...doc.data() } as Conversation);
      });

      return conversations;
    } catch (error) {
      console.error('Get user conversations error:', error);
      return [];
    }
  }

  // Message methods
  static async sendMessage(
    conversationId: string,
    content: string,
    messageType: 'text' | 'voice' | 'image' | 'video' | 'file' = 'text',
    mediaFile?: File | Blob,
    replyToMessageId?: string
  ): Promise<string> {
    try {
      const currentUser = FirebaseAuthService.getCurrentUser();
      if (!currentUser) throw new Error('User not authenticated');

      const userProfile = await FirebaseAuthService.getUserProfile(currentUser.uid);
      if (!userProfile) throw new Error('User profile not found');

      let mediaUrl: string | undefined;
      let mediaSize: number | undefined;

      // Upload media file if provided
      if (mediaFile && messageType !== 'text') {
        const fileName = `${conversationId}/${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const storageRef = ref(storage, `messages/${messageType}s/${fileName}`);
        
        const snapshot = await uploadBytes(storageRef, mediaFile);
        mediaUrl = await getDownloadURL(snapshot.ref);
        mediaSize = mediaFile.size;
      }

      // Create message
      const messageData: Omit<Message, 'id'> = {
        conversationId,
        senderId: currentUser.uid,
        senderName: userProfile.displayName || userProfile.username || 'Unknown',
        content,
        messageType,
        originalLanguage: userProfile.primaryLanguage,
        mediaUrl,
        mediaSize,
        replyToMessageId,
        reactions: {},
        readBy: [currentUser.uid], // Sender has read the message
        deliveredTo: [currentUser.uid],
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp
      };

      const docRef = await addDoc(collection(db, 'messages'), messageData);

      // Update conversation
      await this.updateConversationLastMessage(conversationId, content, currentUser.uid);

      return docRef.id;
    } catch (error) {
      console.error('Send message error:', error);
      throw error;
    }
  }

  static async getConversationMessages(
    conversationId: string,
    limitCount: number = 50
  ): Promise<Message[]> {
    try {
      const messagesRef = collection(db, 'messages');
      const q = query(
        messagesRef,
        where('conversationId', '==', conversationId),
        where('deletedAt', '==', null),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const messages: Message[] = [];

      querySnapshot.forEach((doc) => {
        messages.push({ id: doc.id, ...doc.data() } as Message);
      });

      return messages.reverse(); // Return in chronological order
    } catch (error) {
      console.error('Get conversation messages error:', error);
      return [];
    }
  }

  static subscribeToConversationMessages(
    conversationId: string,
    callback: (messages: Message[]) => void,
    limitCount: number = 50
  ): () => void {
    const messagesRef = collection(db, 'messages');
    const q = query(
      messagesRef,
      where('conversationId', '==', conversationId),
      where('deletedAt', '==', null),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    return onSnapshot(q, (querySnapshot) => {
      const messages: Message[] = [];
      querySnapshot.forEach((doc) => {
        messages.push({ id: doc.id, ...doc.data() } as Message);
      });
      callback(messages.reverse());
    });
  }

  static subscribeToUserConversations(
    userId: string,
    callback: (conversations: Conversation[]) => void
  ): () => void {
    const conversationsRef = collection(db, 'conversations');
    const q = query(
      conversationsRef,
      where('participants', 'array-contains', userId),
      where('isActive', '==', true),
      orderBy('updatedAt', 'desc')
    );

    return onSnapshot(q, (querySnapshot) => {
      const conversations: Conversation[] = [];
      querySnapshot.forEach((doc) => {
        conversations.push({ id: doc.id, ...doc.data() } as Conversation);
      });
      callback(conversations);
    });
  }

  // Utility methods
  static async markMessageAsRead(messageId: string, userId: string): Promise<void> {
    try {
      const messageRef = doc(db, 'messages', messageId);
      await updateDoc(messageRef, {
        readBy: arrayUnion(userId),
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Mark message as read error:', error);
    }
  }

  static async addReaction(messageId: string, emoji: string, userId: string): Promise<void> {
    try {
      const messageRef = doc(db, 'messages', messageId);
      const messageDoc = await getDoc(messageRef);
      
      if (messageDoc.exists()) {
        const messageData = messageDoc.data() as Message;
        const reactions = messageData.reactions || {};
        
        if (!reactions[emoji]) {
          reactions[emoji] = [];
        }
        
        if (!reactions[emoji].includes(userId)) {
          reactions[emoji].push(userId);
        }

        await updateDoc(messageRef, {
          reactions,
          updatedAt: serverTimestamp()
        });
      }
    } catch (error) {
      console.error('Add reaction error:', error);
    }
  }

  static async removeReaction(messageId: string, emoji: string, userId: string): Promise<void> {
    try {
      const messageRef = doc(db, 'messages', messageId);
      const messageDoc = await getDoc(messageRef);
      
      if (messageDoc.exists()) {
        const messageData = messageDoc.data() as Message;
        const reactions = messageData.reactions || {};
        
        if (reactions[emoji]) {
          reactions[emoji] = reactions[emoji].filter(id => id !== userId);
          if (reactions[emoji].length === 0) {
            delete reactions[emoji];
          }
        }

        await updateDoc(messageRef, {
          reactions,
          updatedAt: serverTimestamp()
        });
      }
    } catch (error) {
      console.error('Remove reaction error:', error);
    }
  }

  static async updateTypingStatus(conversationId: string, userId: string, isTyping: boolean): Promise<void> {
    try {
      const conversationRef = doc(db, 'conversations', conversationId);
      
      if (isTyping) {
        await updateDoc(conversationRef, {
          typingUsers: arrayUnion(userId),
          updatedAt: serverTimestamp()
        });
      } else {
        await updateDoc(conversationRef, {
          typingUsers: arrayRemove(userId),
          updatedAt: serverTimestamp()
        });
      }
    } catch (error) {
      console.error('Update typing status error:', error);
    }
  }

  private static async updateConversationLastMessage(
    conversationId: string,
    lastMessage: string,
    senderId: string
  ): Promise<void> {
    try {
      const conversationRef = doc(db, 'conversations', conversationId);
      await updateDoc(conversationRef, {
        lastMessage,
        lastMessageTime: serverTimestamp(),
        lastMessageSender: senderId,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Update conversation last message error:', error);
    }
  }

  static async deleteMessage(messageId: string): Promise<void> {
    try {
      const messageRef = doc(db, 'messages', messageId);
      await updateDoc(messageRef, {
        deletedAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Delete message error:', error);
    }
  }
}
