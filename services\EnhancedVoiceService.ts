import * as Speech from 'expo-speech';
import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';

export interface VoiceRecording {
  uri: string;
  duration: number;
  size: number;
}

export interface SpeechToTextResult {
  text: string;
  confidence: number;
  language?: string;
}

export class EnhancedVoiceService {
  private static recording: Audio.Recording | null = null;
  private static sound: Audio.Sound | null = null;
  private static isInitialized = false;

  static async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await Audio.requestPermissionsAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: false,
      });
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize audio:', error);
      throw error;
    }
  }

  // Text-to-Speech functionality
  static async speakText(text: string, language: string = 'en', options?: {
    pitch?: number;
    rate?: number;
    voice?: string;
  }): Promise<void> {
    const speechOptions = {
      language,
      pitch: options?.pitch || 1.0,
      rate: options?.rate || 0.8,
      voice: options?.voice,
    };

    return new Promise((resolve, reject) => {
      Speech.speak(text, {
        ...speechOptions,
        onDone: () => resolve(),
        onError: (error) => reject(error),
      });
    });
  }

  static stopSpeaking(): void {
    Speech.stop();
  }

  static async getAvailableVoices(): Promise<Speech.Voice[]> {
    return await Speech.getAvailableVoicesAsync();
  }

  static isSpeaking(): boolean {
    return Speech.isSpeakingAsync();
  }

  // Voice Recording functionality
  static async startRecording(): Promise<void> {
    try {
      await this.initialize();

      if (this.recording) {
        await this.stopRecording();
      }

      const recordingOptions = {
        android: {
          extension: '.m4a',
          outputFormat: Audio.RECORDING_OPTION_ANDROID_OUTPUT_FORMAT_MPEG_4,
          audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_AAC,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
        },
        ios: {
          extension: '.m4a',
          outputFormat: Audio.RECORDING_OPTION_IOS_OUTPUT_FORMAT_MPEG4AAC,
          audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_HIGH,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
          linearPCMBitDepth: 16,
          linearPCMIsBigEndian: false,
          linearPCMIsFloat: false,
        },
        web: {
          mimeType: 'audio/webm',
          bitsPerSecond: 128000,
        },
      };

      this.recording = new Audio.Recording();
      await this.recording.prepareToRecordAsync(recordingOptions);
      await this.recording.startAsync();
    } catch (error) {
      console.error('Failed to start recording:', error);
      throw error;
    }
  }

  static async stopRecording(): Promise<VoiceRecording | null> {
    try {
      if (!this.recording) {
        return null;
      }

      await this.recording.stopAndUnloadAsync();
      const uri = this.recording.getURI();
      
      if (!uri) {
        this.recording = null;
        return null;
      }

      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(uri);
      const status = await this.recording.getStatusAsync();
      
      const recording: VoiceRecording = {
        uri,
        duration: status.durationMillis || 0,
        size: fileInfo.exists ? fileInfo.size || 0 : 0,
      };

      this.recording = null;
      return recording;
    } catch (error) {
      console.error('Failed to stop recording:', error);
      this.recording = null;
      return null;
    }
  }

  static async pauseRecording(): Promise<void> {
    if (this.recording) {
      await this.recording.pauseAsync();
    }
  }

  static async resumeRecording(): Promise<void> {
    if (this.recording) {
      await this.recording.startAsync();
    }
  }

  static async getRecordingStatus(): Promise<Audio.RecordingStatus | null> {
    if (this.recording) {
      return await this.recording.getStatusAsync();
    }
    return null;
  }

  static isRecording(): boolean {
    return this.recording !== null;
  }

  // Voice Playback functionality
  static async playVoiceMessage(uri: string): Promise<void> {
    try {
      await this.initialize();

      if (this.sound) {
        await this.sound.unloadAsync();
      }

      const { sound } = await Audio.Sound.createAsync(
        { uri },
        { shouldPlay: true }
      );

      this.sound = sound;

      return new Promise((resolve, reject) => {
        sound.setOnPlaybackStatusUpdate((status) => {
          if (status.isLoaded && status.didJustFinish) {
            resolve();
          } else if (!status.isLoaded && status.error) {
            reject(new Error(status.error));
          }
        });
      });
    } catch (error) {
      console.error('Failed to play voice message:', error);
      throw error;
    }
  }

  static async pausePlayback(): Promise<void> {
    if (this.sound) {
      await this.sound.pauseAsync();
    }
  }

  static async resumePlayback(): Promise<void> {
    if (this.sound) {
      await this.sound.playAsync();
    }
  }

  static async stopPlayback(): Promise<void> {
    if (this.sound) {
      await this.sound.stopAsync();
      await this.sound.unloadAsync();
      this.sound = null;
    }
  }

  static async getPlaybackStatus(): Promise<Audio.AVPlaybackStatus | null> {
    if (this.sound) {
      return await this.sound.getStatusAsync();
    }
    return null;
  }

  static isPlaying(): boolean {
    return this.sound !== null;
  }

  // Speech-to-Text functionality (placeholder for future implementation)
  static async speechToText(audioUri: string): Promise<SpeechToTextResult> {
    // This would integrate with a speech-to-text service like Google Speech-to-Text
    // For now, return a placeholder
    console.warn('Speech-to-text not implemented yet');
    return {
      text: '[Speech-to-text not available]',
      confidence: 0,
      language: 'en'
    };
  }

  // Utility functions
  static formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  static async deleteRecording(uri: string): Promise<void> {
    try {
      const fileInfo = await FileSystem.getInfoAsync(uri);
      if (fileInfo.exists) {
        await FileSystem.deleteAsync(uri);
      }
    } catch (error) {
      console.error('Failed to delete recording:', error);
    }
  }

  static async getRecordingSize(uri: string): Promise<number> {
    try {
      const fileInfo = await FileSystem.getInfoAsync(uri);
      return fileInfo.exists ? fileInfo.size || 0 : 0;
    } catch (error) {
      console.error('Failed to get recording size:', error);
      return 0;
    }
  }

  // Cleanup
  static async cleanup(): Promise<void> {
    try {
      if (this.recording) {
        await this.recording.stopAndUnloadAsync();
        this.recording = null;
      }
      if (this.sound) {
        await this.sound.unloadAsync();
        this.sound = null;
      }
      Speech.stop();
    } catch (error) {
      console.error('Failed to cleanup voice service:', error);
    }
  }
}
