import { FirebaseAuthService } from '@/services/FirebaseAuthService';
import React, { useState } from 'react';
import { Alert, Button, StyleSheet, Text, TextInput, View } from 'react-native';

const OTPScreen = ({ confirm, onSuccess }) => {
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);

  const confirmCode = async () => {
    if (!code || code.length < 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit OTP code');
      return;
    }

    setLoading(true);
    try {
      if (confirm && typeof confirm.confirm === 'function') {
        // For native Firebase auth
        await confirm.confirm(code);
      } else {
        // For web Firebase auth
        await FirebaseAuthService.verifyPhoneCode(confirm, code);
      }
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('OTP confirm error:', error);
      Alert.alert('Invalid code', (error && error.message) || 'Please try again');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Enter OTP Code</Text>
      <Text style={styles.subtitle}>
        We've sent a verification code to your phone number
      </Text>
      
      <TextInput
        style={styles.input}
        value={code}
        onChangeText={setCode}
        placeholder="Enter 6-digit code"
        keyboardType="number-pad"
        maxLength={6}
        autoFocus
      />
      
      <Button
        title={loading ? 'Verifying...' : 'Verify Code'}
        onPress={confirmCode}
        disabled={loading}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 15,
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
    borderRadius: 8,
  },
});

export default OTPScreen;
