rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can read other users' public profiles for search and chat
    match /users/{userId} {
      allow read: if request.auth != null;
    }
    
    // Conversation access for participants only
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.participants;
      allow create: if request.auth != null && 
        request.auth.uid in request.resource.data.participants;
    }
    
    // Message access for conversation participants
    match /messages/{messageId} {
      allow read, write: if request.auth != null && 
        exists(/databases/$(database)/documents/conversations/$(resource.data.conversationId)) &&
        request.auth.uid in get(/databases/$(database)/documents/conversations/$(resource.data.conversationId)).data.participants;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.senderId;
    }
    
    // Translation cache - read/write for authenticated users
    match /translationCache/{cacheId} {
      allow read, write: if request.auth != null;
    }
    
    // Allow read/write access on all documents to authenticated users (for development)
    // Remove this rule in production and use more specific rules above
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
