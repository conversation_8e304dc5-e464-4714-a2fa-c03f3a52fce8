import { ThemedText } from '@/components/ThemedText';
import { useFuturisticTheme } from '@/hooks/useFuturisticTheme';
import React, { useEffect, useRef } from 'react';
import {
    Animated,
    Easing,
    Pressable,
    TextStyle,
    ViewStyle
} from 'react-native';

interface FuturisticAnimatedButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'accent' | 'ghost' | 'neon' | 'cyber' | 'matrix';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  glowEffect?: boolean;
  pulseEffect?: boolean;
}

export default function FuturisticAnimatedButton({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  style,
  textStyle,
  glowEffect = true,
  pulseEffect = false
}: FuturisticAnimatedButtonProps) {
  const { colors } = useFuturisticTheme();
  
  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  // Pulse animation effect
  useEffect(() => {
    if (pulseEffect && !disabled) {
      const pulse = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      );
      pulse.start();
      return () => pulse.stop();
    }
  }, [pulseEffect, disabled]);

  // Shimmer animation effect
  useEffect(() => {
    if (glowEffect && !disabled) {
      const shimmer = Animated.loop(
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      );
      shimmer.start();
      return () => shimmer.stop();
    }
  }, [glowEffect, disabled]);

  const handlePressIn = () => {
    // Simplified - no animations
  };

  const handlePressOut = () => {
    // Simplified - no animations
  };

  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      shadowOffset: { width: 0, height: 4 },
      shadowRadius: 8,
      elevation: 8,
    };

    const sizeStyles = {
      small: { paddingHorizontal: 16, paddingVertical: 8, minHeight: 36 },
      medium: { paddingHorizontal: 24, paddingVertical: 12, minHeight: 48 },
      large: { paddingHorizontal: 32, paddingVertical: 16, minHeight: 56 },
    };

    const variantStyles = {
      primary: {
        backgroundColor: disabled ? colors.textTertiary : colors.primary,
        shadowColor: colors.primary,
      },
      secondary: {
        backgroundColor: disabled ? colors.textTertiary : colors.secondary,
        shadowColor: colors.secondary,
      },
      accent: {
        backgroundColor: disabled ? colors.textTertiary : colors.accent,
        shadowColor: colors.accent,
      },
      neon: {
        backgroundColor: disabled ? colors.textTertiary : colors.neon,
        shadowColor: colors.neon,
      },
      cyber: {
        backgroundColor: disabled ? colors.textTertiary : colors.cyber,
        shadowColor: colors.cyber,
      },
      matrix: {
        backgroundColor: disabled ? colors.textTertiary : colors.matrix,
        shadowColor: colors.matrix,
      },
      ghost: {
        backgroundColor: 'transparent',
        borderWidth: 2,
        borderColor: disabled ? colors.textTertiary : colors.border,
        shadowColor: colors.border,
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
      opacity: disabled ? 0.6 : 1,
    };
  };

  const getTextStyle = (): TextStyle => {
    const sizeStyles = {
      small: { fontSize: 14 },
      medium: { fontSize: 16 },
      large: { fontSize: 18 },
    };

    const variantStyles = {
      primary: { color: 'white', fontWeight: '600' },
      secondary: { color: 'white', fontWeight: '600' },
      accent: { color: 'white', fontWeight: '600' },
      neon: { color: colors.text, fontWeight: '700', textShadowColor: colors.glow, textShadowRadius: 4 },
      cyber: { color: 'white', fontWeight: '700', textShadowColor: colors.glow, textShadowRadius: 4 },
      matrix: { color: colors.text, fontWeight: '700', textShadowColor: colors.glow, textShadowRadius: 4 },
      ghost: { color: disabled ? colors.textTertiary : colors.text, fontWeight: '500' },
    };

    return {
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const shimmerTranslateX = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-100, 100],
  });

  const glowOpacity = glowAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.8],
  });

  return (
    <Pressable
      style={[getButtonStyle(), style, disabled && { opacity: 0.6 }]}
      onPress={onPress}
      disabled={disabled}
    >
      <ThemedText style={[getTextStyle(), textStyle]}>
        {title}
      </ThemedText>
    </Pressable>
  );
}
