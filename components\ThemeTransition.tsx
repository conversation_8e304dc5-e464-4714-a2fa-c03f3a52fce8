import React, { useRef, useEffect } from 'react';
import { Animated, ViewStyle } from 'react-native';
import { useFuturisticTheme } from '@/hooks/useFuturisticTheme';

interface ThemeTransitionProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export default function ThemeTransition({ children, style }: ThemeTransitionProps) {
  const { colors, theme } = useFuturisticTheme();
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const previousTheme = useRef(theme);

  useEffect(() => {
    if (previousTheme.current !== theme) {
      // Animate theme transition
      Animated.sequence([
        // Fade out with slight scale down
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 0.3,
            duration: 150,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 0.98,
            duration: 150,
            useNativeDriver: true,
          }),
        ]),
        // Fade in with scale back to normal
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.spring(scaleAnim, {
            toValue: 1,
            tension: 300,
            friction: 10,
            useNativeDriver: true,
          }),
        ]),
      ]).start();

      previousTheme.current = theme;
    }
  }, [theme, fadeAnim, scaleAnim]);

  return (
    <Animated.View
      style={[
        {
          flex: 1,
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
        style,
      ]}
    >
      {children}
    </Animated.View>
  );
}
