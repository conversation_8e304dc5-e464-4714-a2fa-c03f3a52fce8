# 🔥 Firebase Setup Guide for voXchat

This guide will help you set up Firebase for voXchat, replacing the MySQL database with Firebase's real-time NoSQL database, authentication, and storage services.

## 🚀 Why Firebase?

- **Real-time Database** - Instant message synchronization
- **Built-in Authentication** - Phone authentication
- **Cloud Storage** - Voice messages and media files
- **Serverless** - No backend server management
- **Scalable** - Automatic scaling and global CDN
- **Offline Support** - Works offline with automatic sync

## 📋 Prerequisites

- **Google Account** for Firebase Console access
- **Node.js 18+** and **npm/yarn**
- **Expo CLI** for React Native development

## 🔧 Firebase Project Setup

### 1. Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click **"Create a project"**
3. Enter project name: `voxchat` (or your preferred name)
4. Enable Google Analytics (optional)
5. Click **"Create project"**

### 2. Enable Authentication

1. In Firebase Console, go to **Authentication** > **Sign-in method**
2. Enable these providers:
   - **Phone** ✅
3. Configure authorized domains if needed

### 3. Create Firestore Database

1. Go to **Firestore Database** > **Create database**
2. Choose **"Start in test mode"** (we'll secure it later)
3. Select your preferred location
4. Click **"Done"**

### 4. Set up Cloud Storage

1. Go to **Storage** > **Get started**
2. Choose **"Start in test mode"**
3. Select the same location as Firestore
4. Click **"Done"**

### 5. Get Firebase Configuration

1. Go to **Project Settings** (gear icon)
2. Scroll to **"Your apps"** section
3. Click **"Add app"** > **Web app** (🌐)
4. Enter app nickname: `voxchat-web`
5. Copy the configuration object

## 🔑 Environment Configuration

### 1. Update .env File

Create or update your `.env` file with Firebase credentials:

```bash
# Firebase Configuration
EXPO_PUBLIC_FIREBASE_API_KEY=AIzaSyC...
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=voxchat-12345.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=voxchat-12345
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=voxchat-12345.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
EXPO_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef123456
EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=G-ABCDEF1234

# Backend Configuration (Optional - for enhanced translation)
EXPO_PUBLIC_BACKEND_URL=http://localhost:8000
```

### 2. Firebase Security Rules

Update Firestore security rules in Firebase Console:

```javascript
// Firestore Rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can read other users' public profiles
    match /users/{userId} {
      allow read: if request.auth != null;
    }
    
    // Conversation access for participants only
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.participants;
    }
    
    // Message access for conversation participants
    match /messages/{messageId} {
      allow read, write: if request.auth != null && 
        exists(/databases/$(database)/documents/conversations/$(resource.data.conversationId)) &&
        request.auth.uid in get(/databases/$(database)/documents/conversations/$(resource.data.conversationId)).data.participants;
    }
    
    // Translation cache - read/write for authenticated users
    match /translationCache/{cacheId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 3. Storage Security Rules

Update Storage security rules:

```javascript
// Storage Rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Voice messages and media files
    match /messages/{allPaths=**} {
      allow read, write: if request.auth != null;
    }
    
    // User profile pictures
    match /profiles/{userId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## 🧪 Testing Firebase Setup

### 1. Install Dependencies

```bash
npm install firebase
```

### 2. Test Firebase Connection

Create a test file to verify connection:

```typescript
// test-firebase.ts
import { FirebaseAuthService } from './services/FirebaseAuthService';
import { FirebaseChatService } from './services/FirebaseChatService';

async function testFirebase() {
  try {
    // Test authentication
    console.log('Testing Firebase connection...');

    // Test user creation (use your own email)
    const user = await FirebaseAuthService.signUpWithEmail(
      '<EMAIL>',
      'your-password',
      'Your Name'
    );

    console.log('✅ User created:', user.uid);

    // Test conversation creation
    const conversationId = await FirebaseChatService.createDirectConversation('another-user-id');
    console.log('✅ Conversation created:', conversationId);

    // Test message sending
    const messageId = await FirebaseChatService.sendMessage(
      conversationId,
      'Hello Firebase!',
      'text'
    );
    console.log('✅ Message sent:', messageId);

    console.log('🎉 Firebase setup successful!');
  } catch (error) {
    console.error('❌ Firebase test failed:', error);
  }
}

testFirebase();
```

### 3. Run the App

```bash
# Start the app
npx expo start

# Test authentication
# - Try signing up with a new account
# - Try signing in with existing account
# - Test the demo users (alice, bob, charlie)
```

## 📱 App Features with Firebase

### ✅ **Authentication**
- Email/password signup and login
- User profile creation and management
- Automatic session management
- Password reset functionality

### ✅ **Real-time Messaging**
- Instant message delivery
- Typing indicators
- Message read receipts
- Conversation management

### ✅ **Translation**
- Message translation with caching
- Language detection
- Translation history

### ✅ **Voice Messages**
- Voice recording and upload
- Voice message playback
- Audio file storage in Firebase Storage

## 🔧 Advanced Configuration

### 1. Enable Offline Persistence

```typescript
// In FirebaseService.ts
import { enableNetwork, disableNetwork } from 'firebase/firestore';

// Enable offline persistence
export const enableOfflineMode = () => disableNetwork(db);
export const enableOnlineMode = () => enableNetwork(db);
```

### 2. Add Push Notifications

```bash
# Install Firebase messaging
npm install @react-native-firebase/messaging

# Configure in FirebaseService.ts
import messaging from '@react-native-firebase/messaging';
```

### 3. Analytics and Crashlytics

```bash
# Install analytics
npm install @react-native-firebase/analytics
npm install @react-native-firebase/crashlytics
```

## 🐛 Troubleshooting

### Common Issues

1. **"Firebase not initialized"**
   ```bash
   # Check .env file has correct Firebase config
   # Verify EXPO_PUBLIC_ prefix on all variables
   ```

2. **"Permission denied"**
   ```bash
   # Check Firestore security rules
   # Ensure user is authenticated
   ```

3. **"Network error"**
   ```bash
   # Check internet connection
   # Verify Firebase project is active
   ```

### Debug Mode

Enable Firebase debug logging:

```typescript
// In FirebaseService.ts
import { connectFirestoreEmulator } from 'firebase/firestore';

// For development only
if (__DEV__) {
  connectFirestoreEmulator(db, 'localhost', 8080);
}
```

## 🚀 Deployment

### 1. Production Security Rules

Update rules for production:

```javascript
// More restrictive rules for production
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 2. Environment Variables

Set production environment variables:

```bash
# Production .env
EXPO_PUBLIC_FIREBASE_API_KEY=your-prod-api-key
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-prod-project-id
# ... other prod configs
```

## 🎉 Success!

Your voXchat app is now powered by Firebase! You have:

- ✅ **Real-time messaging** with Firestore
- ✅ **User authentication** with Firebase Auth
- ✅ **File storage** with Firebase Storage
- ✅ **Offline support** with automatic sync
- ✅ **Scalable infrastructure** that grows with your app

## 📞 Support

- **Firebase Documentation**: https://firebase.google.com/docs
- **Expo Firebase Guide**: https://docs.expo.dev/guides/using-firebase/
- **GitHub Issues**: https://github.com/Raman247365/voXchat/issues

Happy chatting with Firebase! 🔥
