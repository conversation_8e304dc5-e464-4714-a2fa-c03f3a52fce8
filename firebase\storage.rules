rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Voice messages and media files
    match /messages/{messageType}/{fileName} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        request.auth.uid != null;
    }
    
    // User profile pictures
    match /profiles/{userId}/{fileName} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        request.auth.uid == userId;
    }
    
    // Allow read/write access on all files to authenticated users (for development)
    // Remove this rule in production and use more specific rules above
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
