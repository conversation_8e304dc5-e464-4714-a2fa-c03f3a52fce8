export interface ThemeColors {
  background: string;
  surface: string;
  surfaceHover: string;
  surfaceActive: string;
  primary: string;
  primaryHover: string;
  primaryActive: string;
  secondary: string;
  secondaryHover: string;
  text: string;
  textSecondary: string;
  textTertiary: string;
  border: string;
  borderHover: string;
  accent: string;
  accentHover: string;
  success: string;
  successHover: string;
  warning: string;
  warningHover: string;
  error: string;
  errorHover: string;
  shadow: string;
  shadowHover: string;
  overlay: string;
  glass: string;
  gradient: string;
  // New futuristic properties
  neon: string;
  neonHover: string;
  glow: string;
  glowHover: string;
  cyber: string;
  cyberHover: string;
  hologram: string;
  hologramHover: string;
  matrix: string;
  matrixHover: string;
}

export const FuturisticThemes: Record<string, ThemeColors> = {
  white: {
    background: '#FFFFFF',
    surface: '#FAFAFA',
    surfaceHover: '#F5F5F5',
    surfaceActive: '#F0F0F0',
    primary: '#000000',
    primaryHover: '#333333',
    primaryActive: '#000000',
    secondary: '#666666',
    secondaryHover: '#555555',
    text: '#000000',
    textSecondary: '#666666',
    textTertiary: '#999999',
    border: '#E0E0E0',
    borderHover: '#F0F0F0',
    accent: '#000000',
    accentHover: '#333333',
    success: '#000000',
    successHover: '#333333',
    warning: '#666666',
    warningHover: '#555555',
    error: '#000000',
    errorHover: '#333333',
    shadow: 'rgba(0, 0, 0, 0.04)',
    shadowHover: 'rgba(0, 0, 0, 0.08)',
    overlay: 'rgba(0, 0, 0, 0.6)',
    glass: 'rgba(255, 255, 255, 0.9)',
    gradient: 'linear-gradient(135deg, #1A1A1A 0%, #333333 50%, #666666 100%)',
    // Minimal futuristic properties
    neon: '#1A1A1A',
    neonHover: '#333333',
    glow: 'rgba(26, 26, 26, 0.2)',
    glowHover: 'rgba(26, 26, 26, 0.3)',
    cyber: '#333333',
    cyberHover: '#1A1A1A',
    hologram: 'rgba(26, 26, 26, 0.05)',
    hologramHover: 'rgba(26, 26, 26, 0.1)',
    matrix: '#666666',
    matrixHover: '#555555',
  },
  superBlack: {
    background: '#000000',
    surface: '#0A0A0A',
    surfaceHover: '#1A1A1A',
    surfaceActive: '#222222',
    primary: '#FFFFFF',
    primaryHover: '#C0C0C0',
    primaryActive: '#FFFFFF',
    secondary: '#888888',
    secondaryHover: '#999999',
    text: '#FFFFFF',
    textSecondary: '#888888',
    textTertiary: '#444444',
    border: '#222222',
    borderHover: '#111111',
    accent: '#00E5FF',
    accentHover: '#00C4E6',
    success: '#FFFFFF',
    successHover: '#C0C0C0',
    warning: '#888888',
    warningHover: '#999999',
    error: '#FFFFFF',
    errorHover: '#C0C0C0',
    shadow: 'rgba(255, 255, 255, 0.04)',
    shadowHover: 'rgba(255, 255, 255, 0.08)',
    overlay: 'rgba(0, 0, 0, 0.9)',
    glass: 'rgba(10, 10, 10, 0.9)',
    gradient: 'linear-gradient(135deg, #000000 0%, #1A1A1A 50%, #000000 100%)',
    // Cutting-edge minimal properties
    neon: '#00E5FF',
    neonHover: '#00C4E6',
    glow: 'rgba(0, 229, 255, 0.2)',
    glowHover: 'rgba(0, 229, 255, 0.3)',
    cyber: '#FFFFFF',
    cyberHover: '#C0C0C0',
    hologram: 'rgba(255, 255, 255, 0.05)',
    hologramHover: 'rgba(255, 255, 255, 0.1)',
    matrix: '#888888',
    matrixHover: '#999999',
  },
};