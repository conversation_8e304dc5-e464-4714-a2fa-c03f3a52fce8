import { useFuturisticTheme } from '@/hooks/useFuturisticTheme';
import React from 'react';
import {
    StyleSheet,
    Text,
    TouchableOpacity
} from 'react-native';

interface ThemeToggleProps {
  onToggle: () => void;
}

export default function ThemeToggle({ onToggle }: ThemeToggleProps) {
  const { theme } = useFuturisticTheme();

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: theme === 'white' ? '#1A1A1A' : '#C0C0C0',
        },
      ]}
      onPress={onToggle}
      activeOpacity={0.8}
    >
      <Text
        style={[
          styles.buttonText,
          {
            color: theme === 'white' ? '#FFFFFF' : '#000000',
          },
        ]}
      >
        {theme === 'white' ? '●' : '○'}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 60,
    right: 20,
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    zIndex: 1000,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    letterSpacing: 0.3,
  },
});
