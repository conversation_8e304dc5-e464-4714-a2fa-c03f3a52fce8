import {
  createUserWithEmailAndPassword,
  onAuthStateChanged,
  sendPasswordResetEmail,
  signInWithEmailAndPassword,
  signOut,
  updateProfile,
  User
} from 'firebase/auth';
import { doc, getDoc, serverTimestamp, setDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from './FirebaseService';

export interface UserProfile {
  uid: string;
  email?: string;
  displayName?: string;
  phoneNumber?: string;
  photoURL?: string;
  username?: string;
  primaryLanguage: string;
  secondaryLanguages: string[];
  autoTranslate: boolean;
  voiceEnabled: boolean;
  signLanguageEnabled: boolean;
  fontSize: 'small' | 'medium' | 'large';
  status: 'online' | 'away' | 'busy' | 'offline';
  statusMessage?: string;
  lastSeen: any;
  createdAt: any;
  updatedAt: any;
}

export class FirebaseAuthService {
  private static currentUser: User | null = null;
  private static authStateListeners: ((user: User | null) => void)[] = [];

  static initialize() {
    onAuthStateChanged(auth, (user) => {
      this.currentUser = user;
      this.authStateListeners.forEach(listener => listener(user));
      
      if (user) {
        // Update user status to online
        this.updateUserStatus('online');
      }
    });
  }

  static onAuthStateChanged(callback: (user: User | null) => void) {
    this.authStateListeners.push(callback);
    // Call immediately with current state
    callback(this.currentUser);
    
    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(callback);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  static getCurrentUser(): User | null {
    return this.currentUser;
  }



  static async signInWithEmail(email: string, password: string): Promise<User> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Check if user profile exists, create if not
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      if (!userDoc.exists()) {
        await this.createUserProfile(user);
      }

      return user;
    } catch (error: any) {
      console.error('Email sign in error:', error);

      // Handle specific Firebase errors
      if (error.code === 'auth/user-not-found') {
        throw new Error('No account found with this email address.');
      } else if (error.code === 'auth/wrong-password') {
        throw new Error('Incorrect password.');
      } else if (error.code === 'auth/invalid-email') {
        throw new Error('Invalid email address.');
      } else if (error.code === 'auth/user-disabled') {
        throw new Error('This account has been disabled.');
      } else if (error.code === 'auth/too-many-requests') {
        throw new Error('Too many failed attempts. Please try again later.');
      }

      throw error;
    }
  }

  static async signUpWithEmail(email: string, password: string, displayName?: string): Promise<User> {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Update display name if provided
      if (displayName) {
        await updateProfile(user, { displayName });
      }

      // Create user profile
      await this.createUserProfile(user, { displayName });

      return user;
    } catch (error: any) {
      console.error('Email sign up error:', error);

      // Handle specific Firebase errors
      if (error.code === 'auth/email-already-in-use') {
        throw new Error('An account with this email already exists.');
      } else if (error.code === 'auth/invalid-email') {
        throw new Error('Invalid email address.');
      } else if (error.code === 'auth/weak-password') {
        throw new Error('Password should be at least 6 characters.');
      }

      throw error;
    }
  }

  static async resetPassword(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      console.error('Password reset error:', error);

      // Handle specific Firebase errors
      if (error.code === 'auth/user-not-found') {
        throw new Error('No account found with this email address.');
      } else if (error.code === 'auth/invalid-email') {
        throw new Error('Invalid email address.');
      }

      throw error;
    }
  }





  static async signOut(): Promise<void> {
    try {
      // Update status to offline before signing out
      if (this.currentUser) {
        await this.updateUserStatus('offline');
      }
      await signOut(auth);
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }



  static async createUserProfile(user: User, additionalData?: Partial<UserProfile>): Promise<void> {
    try {
      const userProfile: UserProfile = {
        uid: user.uid,
        email: user.email || undefined,
        displayName: user.displayName || additionalData?.displayName || '',
        phoneNumber: user.phoneNumber || undefined,
        photoURL: user.photoURL || undefined,
        username: additionalData?.username || user.displayName?.toLowerCase().replace(/\s+/g, '') || '',
        primaryLanguage: 'en',
        secondaryLanguages: [],
        autoTranslate: true,
        voiceEnabled: true,
        signLanguageEnabled: false,
        fontSize: 'medium',
        status: 'online',
        statusMessage: '',
        lastSeen: serverTimestamp(),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        ...additionalData
      };

      await setDoc(doc(db, 'users', user.uid), userProfile);
    } catch (error) {
      console.error('Create user profile error:', error);
      throw error;
    }
  }

  static async getUserProfile(uid: string): Promise<UserProfile | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid));
      if (userDoc.exists()) {
        return userDoc.data() as UserProfile;
      }
      return null;
    } catch (error) {
      console.error('Get user profile error:', error);
      return null;
    }
  }

  static async updateUserProfile(uid: string, updates: Partial<UserProfile>): Promise<void> {
    try {
      await updateDoc(doc(db, 'users', uid), {
        ...updates,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Update user profile error:', error);
      throw error;
    }
  }

  static async updateUserStatus(status: 'online' | 'away' | 'busy' | 'offline', statusMessage?: string): Promise<void> {
    try {
      if (!this.currentUser) return;

      await updateDoc(doc(db, 'users', this.currentUser.uid), {
        status,
        statusMessage: statusMessage || '',
        lastSeen: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Update user status error:', error);
    }
  }

  static async searchUsers(query: string, limit: number = 20): Promise<UserProfile[]> {
    try {
      // Note: Firestore doesn't support full-text search natively
      // You might want to use Algolia or implement a different search strategy
      // This is a simplified version
      const { collection, getDocs, query: firestoreQuery, where, orderBy, limitToLast } = await import('firebase/firestore');
      
      const usersRef = collection(db, 'users');
      const q = firestoreQuery(
        usersRef,
        where('username', '>=', query.toLowerCase()),
        where('username', '<=', query.toLowerCase() + '\uf8ff'),
        orderBy('username'),
        limitToLast(limit)
      );

      const querySnapshot = await getDocs(q);
      const users: UserProfile[] = [];
      
      querySnapshot.forEach((doc) => {
        users.push(doc.data() as UserProfile);
      });

      return users;
    } catch (error) {
      console.error('Search users error:', error);
      return [];
    }
  }
}
