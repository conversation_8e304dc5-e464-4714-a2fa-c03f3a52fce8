# Phone Authentication Setup

## Firebase Console Configuration

### 1. Enable Phone Authentication
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to **Authentication > Sign-in method**
4. Click **Phone** provider
5. Toggle **Enable**
6. Click **Save**

### 2. Android Configuration
1. In Firebase Console > Project Settings
2. Go to "Your apps" section
3. Select your Android app
4. Add your **SHA-1 fingerprint**:
   ```bash
   # Debug keystore (development)
   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
   
   # Or for Expo managed workflow
   expo credentials:manager
   ```
5. Download updated `google-services.json`
6. Place it in `android/app/` directory

### 3. Add Authorized Domains (Web)
1. In Authentication > Settings > Authorized domains
2. Add your domains:
   - `localhost` (for development)
   - Your production domain
   - `your-project.web.app` (if using Firebase Hosting)

### 4. Test Phone Numbers (Optional)
For testing without sending real SMS:
1. In Firebase Console > Authentication > Settings
2. <PERSON>roll to "Phone numbers for testing"
3. Add test phone numbers with verification codes
   - Example: `******-555-3434` with code `123456`

## Android App Verification

Firebase uses these methods to verify your Android app:
1. **Play Integrity API** (primary) - Automatic on devices with Google Play
2. **reCAPTCHA** (fallback) - When Play Integrity unavailable

### For Expo Managed Workflow:
```bash
# Build development build with phone auth
eas build --profile development --platform android
```

### For Bare React Native:
Ensure `google-services.json` is in `android/app/` directory

## Usage

```typescript
// Send OTP
const confirmation = await FirebaseAuthService.signInWithPhoneNumber('+1234567890');

// Verify OTP
const user = await FirebaseAuthService.verifyPhoneCode(confirmation, '123456');
```

## Troubleshooting

### Common Errors:
- **auth/invalid-app-credential**: Phone auth not enabled in Firebase Console
- **auth/app-not-authorized**: Missing SHA-1 fingerprint in Firebase Console
- **auth/too-many-requests**: Rate limited, try again later
- **auth/invalid-phone-number**: Use international format (+1234567890)
- **auth/quota-exceeded**: SMS quota exceeded, try again later

### Android Specific:
- Ensure `google-services.json` is up to date
- Add SHA-1 fingerprint for both debug and release keystores
- For Expo: Use development build, not Expo Go
- Test with fictional phone numbers during development

### Testing:
```typescript
// Add test phone number in Firebase Console
// Phone: ******-555-3434, Code: 123456
const confirmation = await FirebaseAuthService.signInWithPhoneNumber('+16505553434');
const user = await FirebaseAuthService.verifyPhoneCode(confirmation, '123456');
```