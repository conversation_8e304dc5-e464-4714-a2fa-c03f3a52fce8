import React, { useState } from 'react';
import {
  TextInput,
  View,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
} from 'react-native';
import { useFuturisticTheme } from '@/hooks/useFuturisticTheme';
import { ComponentSize, Typography, BorderRadius } from '@/constants/DesignTokens';

interface CuttingEdgeInputProps extends TextInputProps {
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  variant?: 'default' | 'minimal';
}

export default function CuttingEdgeInput({
  containerStyle,
  inputStyle,
  variant = 'default',
  onFocus,
  onBlur,
  ...props
}: CuttingEdgeInputProps) {
  const { colors, theme } = useFuturisticTheme();
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = (e: any) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      height: ComponentSize.input.height,
      paddingHorizontal: ComponentSize.input.paddingHorizontal,
      borderRadius: BorderRadius.medium,
      justifyContent: 'center',
      borderWidth: 1,
    };

    if (variant === 'minimal') {
      return {
        ...baseStyle,
        backgroundColor: 'transparent',
        borderColor: isFocused ? colors.accent : colors.border,
        borderWidth: isFocused ? 2 : 1,
      };
    }

    // Default variant
    return {
      ...baseStyle,
      backgroundColor: theme === 'white' ? colors.background : colors.surface,
      borderColor: isFocused ? colors.accent : colors.border,
      borderWidth: isFocused ? 2 : 1,
    };
  };

  const getInputStyle = (): TextStyle => {
    return {
      fontSize: Typography.fontSize.body,
      fontWeight: Typography.fontWeight.regular,
      color: colors.text,
      backgroundColor: 'transparent',
      padding: 0,
      margin: 0,
      letterSpacing: Typography.letterSpacing.body,
    };
  };

  const getPlaceholderColor = (): string => {
    return colors.textTertiary;
  };

  return (
    <View style={[getContainerStyle(), containerStyle]}>
      <TextInput
        style={[getInputStyle(), inputStyle]}
        placeholderTextColor={getPlaceholderColor()}
        onFocus={handleFocus}
        onBlur={handleBlur}
        {...props}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  // Additional styles if needed
});
