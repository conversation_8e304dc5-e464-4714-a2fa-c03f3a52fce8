import React from 'react';
import { StyleSheet, TouchableOpacity, ViewStyle, TextStyle } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { useFuturisticTheme } from '@/hooks/useFuturisticTheme';

interface FuturisticButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'accent' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export default function FuturisticButton({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  style,
  textStyle
}: FuturisticButtonProps) {
  const { colors } = useFuturisticTheme();

  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    };

    const sizeStyles = {
      small: { height: 36, paddingHorizontal: 16 },
      medium: { height: 44, paddingHorizontal: 20 },
      large: { height: 52, paddingHorizontal: 24 },
    };

    const variantStyles = {
      primary: {
        backgroundColor: disabled ? colors.textTertiary : colors.primary,
      },
      secondary: {
        backgroundColor: disabled ? colors.textTertiary : colors.secondary,
      },
      accent: {
        backgroundColor: disabled ? colors.textTertiary : colors.accent,
      },
      ghost: {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: disabled ? colors.textTertiary : colors.border,
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
      opacity: disabled ? 0.6 : 1,
    };
  };

  const getTextStyle = (): TextStyle => {
    const sizeStyles = {
      small: { fontSize: 14 },
      medium: { fontSize: 16 },
      large: { fontSize: 18 },
    };

    const variantStyles = {
      primary: { color: 'white', fontWeight: '600' },
      secondary: { color: 'white', fontWeight: '600' },
      accent: { color: 'white', fontWeight: '600' },
      ghost: { color: disabled ? colors.textTertiary : colors.text, fontWeight: '500' },
    };

    return {
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <ThemedText style={[getTextStyle(), textStyle]}>
        {title}
      </ThemedText>
    </TouchableOpacity>
  );
}