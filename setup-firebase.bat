@echo off
echo 🔥 Setting up voXchat with Firebase...
echo.

echo 📦 Installing Firebase dependencies...
npm install firebase

if %errorlevel% neq 0 (
    echo ❌ Failed to install Firebase dependencies
    pause
    exit /b 1
)

echo.
echo ✅ Firebase dependencies installed successfully!
echo.

echo 🔧 Setting up environment configuration...

if not exist .env (
    echo 📄 Creating .env file from template...
    copy .env.example .env
    echo.
    echo ⚠️  IMPORTANT: Please update .env file with your Firebase credentials
    echo.
    echo 📋 To get Firebase credentials:
    echo    1. Go to https://console.firebase.google.com/
    echo    2. Create a new project or select existing one
    echo    3. Go to Project Settings ^> General
    echo    4. Scroll to "Your apps" and add a Web app
    echo    5. Copy the configuration values to .env file
    echo.
    echo 🔑 Required environment variables:
    echo    - EXPO_PUBLIC_FIREBASE_API_KEY
    echo    - EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN
    echo    - EXPO_PUBLIC_FIREBASE_PROJECT_ID
    echo    - EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET
    echo    - EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID
    echo    - EXPO_PUBLIC_FIREBASE_APP_ID
    echo.
    pause
    notepad .env
) else (
    echo ✅ .env file already exists
)

echo.
echo 🧪 Testing Firebase setup...

REM Create a simple test file
echo import { FirebaseAuthService } from './services/FirebaseAuthService'; > test-firebase.js
echo console.log('Testing Firebase connection...'); >> test-firebase.js
echo FirebaseAuthService.initialize(); >> test-firebase.js
echo console.log('✅ Firebase initialized successfully!'); >> test-firebase.js

node test-firebase.js 2>nul

if %errorlevel% equ 0 (
    echo ✅ Firebase test passed!
) else (
    echo ⚠️  Firebase test failed - please check your configuration
)

del test-firebase.js 2>nul

echo.
echo 📚 Next steps:
echo    1. Configure Firebase project:
echo       - Enable Authentication (Email/Password)
echo       - Create Firestore database
echo       - Set up Cloud Storage
echo    2. Update security rules (see FIREBASE_SETUP.md)
echo    3. Start the app: npx expo start
echo.

echo 📖 For detailed setup instructions, see:
echo    - FIREBASE_SETUP.md
echo    - https://github.com/Raman247365/voXchat
echo.

echo 🎉 Firebase setup complete!
echo.
pause
