import FuturisticAnimatedButton from '@/components/FuturisticAnimatedButton';
import ThemeTransition from '@/components/ThemeTransition';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useFuturisticTheme } from '@/hooks/useFuturisticTheme';
import React, { useState } from 'react';
import { ScrollView, StyleSheet, Switch, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function SettingsScreen() {
  const { colors, theme, toggleTheme } = useFuturisticTheme();
  const [autoTranslate, setAutoTranslate] = useState(true);
  const [voiceEnabled, setVoiceEnabled] = useState(true);
  const [signLanguage, setSignLanguage] = useState(false);

  const SettingItem = ({ icon, title, subtitle, onPress, rightElement }: {
    icon: string;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightElement?: React.ReactNode;
  }) => (
    <TouchableOpacity
      style={[styles.settingItem, {
        borderBottomColor: colors.border,
        backgroundColor: colors.surface
      }]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.settingLeft}>
        <IconSymbol name={icon} size={24} color={colors.primary} style={styles.settingIcon} />
        <View>
          <ThemedText type="defaultSemiBold" style={{ color: colors.text }}>{title}</ThemedText>
          {subtitle && <ThemedText style={[styles.subtitle, { color: colors.textSecondary }]}>{subtitle}</ThemedText>}
        </View>
      </View>
      {rightElement || <IconSymbol name="chevron.right" size={16} color={colors.textSecondary} />}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ThemeTransition>
        <ScrollView style={styles.scrollContent}>
          <View style={styles.header}>
            <ThemedText type="title" style={{ color: colors.text }}>Settings</ThemedText>
          </View>

      <View style={styles.section}>
        <ThemedText type="subtitle" style={[styles.sectionTitle, { color: colors.textSecondary }]}>Language & Translation</ThemedText>
        <SettingItem
          icon="globe"
          title="Default Language"
          subtitle="English"
        />
        <SettingItem
          icon="arrow.2.squarepath"
          title="Auto-translate messages"
          rightElement={<Switch value={autoTranslate} onValueChange={setAutoTranslate} />}
        />
      </View>

      <View style={styles.section}>
        <ThemedText type="subtitle" style={[styles.sectionTitle, { color: colors.textSecondary }]}>Accessibility</ThemedText>
        <SettingItem
          icon="speaker.wave.2"
          title="Voice Features"
          subtitle="Text-to-speech and voice messages"
          rightElement={<Switch value={voiceEnabled} onValueChange={setVoiceEnabled} />}
        />
        <SettingItem
          icon="hand.raised"
          title="Sign Language Support"
          subtitle="Camera-based gesture recognition"
          rightElement={<Switch value={signLanguage} onValueChange={setSignLanguage} />}
        />
        <SettingItem
          icon="textformat.size"
          title="Font Size"
          subtitle="Medium"
        />
      </View>

      <View style={styles.section}>
        <ThemedText type="subtitle" style={[styles.sectionTitle, { color: colors.textSecondary }]}>Appearance</ThemedText>
        <SettingItem
          icon="paintbrush"
          title="Theme"
          subtitle={theme === 'white' ? 'White' : 'Black'}
        />
        <View style={styles.themeToggleContainer}>
          <FuturisticAnimatedButton
            title={theme === 'white' ? 'Switch to Black' : 'Switch to White'}
            onPress={toggleTheme}
            variant={theme === 'white' ? 'neon' : 'cyber'}
            size="medium"
            glowEffect={false}
            pulseEffect={false}
          />
        </View>
      </View>

      <View style={styles.section}>
        <ThemedText type="subtitle" style={[styles.sectionTitle, { color: colors.textSecondary }]}>Account</ThemedText>
        <SettingItem
          icon="person.circle"
          title="Profile"
        />
        <SettingItem
          icon="lock"
          title="Privacy & Security"
        />
      </View>
        </ScrollView>
      </ThemeTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    paddingHorizontal: 20,
    paddingBottom: 10,
    opacity: 0.7,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 16,
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.6,
    marginTop: 2,
  },
  themeToggleContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
});