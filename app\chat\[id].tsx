import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, FlatList, TextInput, TouchableOpacity, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useFuturisticTheme } from '@/hooks/useFuturisticTheme';
import { FirebaseChatService, Message } from '@/services/FirebaseChatService';
import { FirebaseTranslationService } from '@/services/FirebaseTranslationService';
import { FirebaseAuthService } from '@/services/FirebaseAuthService';
import { EnhancedVoiceService } from '@/services/EnhancedVoiceService';
import { useLocalSearchParams, router } from 'expo-router';

export default function ChatScreen() {
  const { id } = useLocalSearchParams();
  const { colors } = useFuturisticTheme();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    initializeChat();
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [id]);

  const initializeChat = async () => {
    try {
      const user = FirebaseAuthService.getCurrentUser();
      if (!user) {
        router.replace('/');
        return;
      }

      setCurrentUser(user);
      
      if (typeof id === 'string') {
        // Subscribe to messages
        const unsubscribe = FirebaseChatService.subscribeToConversationMessages(
          id,
          (newMessages) => {
            setMessages(newMessages);
            setLoading(false);
          }
        );

        return unsubscribe;
      }
    } catch (error) {
      console.error('Error initializing chat:', error);
      setLoading(false);
    }
  };

  const sendMessage = async () => {
    if (!inputText.trim() || !id || typeof id !== 'string' || !currentUser) return;

    const messageContent = inputText.trim();
    setInputText('');

    try {
      await FirebaseChatService.sendMessage(id, messageContent, 'text');
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message');
    }
  };

  const handleTypingChange = (text: string) => {
    setInputText(text);

    if (!currentUser || !id || typeof id !== 'string') return;

    const isCurrentlyTyping = text.length > 0;

    if (isCurrentlyTyping !== isTyping) {
      setIsTyping(isCurrentlyTyping);
      FirebaseChatService.updateTypingStatus(id, currentUser.uid, isCurrentlyTyping);
    }

    // Clear typing indicator after 2 seconds of inactivity
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    if (isCurrentlyTyping) {
      typingTimeoutRef.current = setTimeout(() => {
        setIsTyping(false);
        FirebaseChatService.updateTypingStatus(id, currentUser.uid, false);
      }, 2000);
    }
  };

  const handleVoiceRecording = async () => {
    try {
      if (isRecording) {
        const recording = await EnhancedVoiceService.stopRecording();
        setIsRecording(false);

        if (recording && recording.uri && id && typeof id === 'string') {
          // Convert recording to blob and send as voice message
          const response = await fetch(recording.uri);
          const blob = await response.blob();
          
          await FirebaseChatService.sendMessage(
            id,
            `Voice message (${EnhancedVoiceService.formatDuration(recording.duration)})`,
            'voice',
            blob
          );
        }
      } else {
        await EnhancedVoiceService.startRecording();
        setIsRecording(true);
      }
    } catch (error) {
      console.error('Voice recording error:', error);
      setIsRecording(false);
      Alert.alert('Error', 'Failed to record voice message');
    }
  };

  const translateMessage = async (message: Message, targetLanguage: string) => {
    try {
      const result = await FirebaseTranslationService.translateText(message.content, targetLanguage);
      
      // Update message with translation
      const updatedMessages = messages.map(msg => {
        if (msg.id === message.id) {
          return {
            ...msg,
            translatedContent: {
              ...msg.translatedContent,
              [targetLanguage]: result.translated
            }
          };
        }
        return msg;
      });
      
      setMessages(updatedMessages);
    } catch (error) {
      console.error('Translation error:', error);
      Alert.alert('Error', 'Failed to translate message');
    }
  };

  const renderMessage = ({ item }: { item: Message }) => {
    const isOwnMessage = item.senderId === currentUser?.uid;
    const showTranslation = item.translatedContent && Object.keys(item.translatedContent).length > 0;

    return (
      <View style={[
        styles.messageContainer,
        isOwnMessage ? styles.ownMessage : styles.otherMessage
      ]}>
        <View style={[
          styles.messageBubble,
          {
            backgroundColor: isOwnMessage ? colors.primary : colors.surface,
            borderColor: colors.border
          }
        ]}>
          <ThemedText style={[
            styles.messageText,
            { color: isOwnMessage ? 'white' : colors.text }
          ]}>
            {item.content}
          </ThemedText>

          {showTranslation && (
            <View style={styles.translationContainer}>
              {Object.entries(item.translatedContent!).map(([lang, translation]) => (
                <ThemedText
                  key={lang}
                  style={[styles.translationText, { color: colors.textSecondary }]}
                >
                  [{lang.toUpperCase()}] {translation}
                </ThemedText>
              ))}
            </View>
          )}

          <View style={styles.messageFooter}>
            <ThemedText style={[
              styles.messageTime,
              { color: isOwnMessage ? 'rgba(255,255,255,0.7)' : colors.textSecondary }
            ]}>
              {formatTime(item.createdAt)}
            </ThemedText>

            {!isOwnMessage && (
              <TouchableOpacity
                style={styles.translateButton}
                onPress={() => translateMessage(item, 'en')}
              >
                <IconSymbol name="globe" size={12} color={colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    );
  };

  const formatTime = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: colors.background }]}>
        <ThemedText style={{ color: colors.text }}>Loading chat...</ThemedText>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView 
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={() => router.back()}>
          <IconSymbol name="chevron.left" size={24} color={colors.text} />
        </TouchableOpacity>
        <ThemedText type="defaultSemiBold" style={{ color: colors.text }}>
          Chat
        </ThemedText>
        <TouchableOpacity>
          <IconSymbol name="phone" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
      />

      {otherUserTyping && (
        <View style={[styles.typingIndicator, { backgroundColor: colors.surface }]}>
          <ThemedText style={[styles.typingText, { color: colors.textSecondary }]}>
            Someone is typing...
          </ThemedText>
        </View>
      )}

      <View style={[styles.inputContainer, { borderTopColor: colors.border }]}>
        <TextInput
          style={[styles.textInput, { 
            backgroundColor: colors.surface,
            color: colors.text,
            borderColor: colors.border
          }]}
          value={inputText}
          onChangeText={handleTypingChange}
          placeholder="Type a message..."
          placeholderTextColor={colors.textSecondary}
          multiline
          maxLength={1000}
          editable={!isRecording}
        />
        
        <TouchableOpacity 
          style={[styles.voiceButton, isRecording && { backgroundColor: colors.error }]}
          onPress={handleVoiceRecording}
        >
          <IconSymbol 
            name={isRecording ? "stop.fill" : "mic"} 
            size={20} 
            color={isRecording ? 'white' : colors.textSecondary} 
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.sendButton, { backgroundColor: colors.primary }]}
          onPress={sendMessage}
          disabled={!inputText.trim()}
        >
          <IconSymbol name="arrow.up" size={20} color="white" />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
  },
  messageContainer: {
    marginVertical: 4,
  },
  ownMessage: {
    alignItems: 'flex-end',
  },
  otherMessage: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
    borderWidth: 1,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  translationContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.2)',
  },
  translationText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  messageTime: {
    fontSize: 12,
  },
  translateButton: {
    padding: 4,
  },
  typingIndicator: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 16,
    borderRadius: 16,
    marginBottom: 8,
  },
  typingText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  textInput: {
    flex: 1,
    minHeight: 40,
    maxHeight: 100,
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 8,
    fontSize: 16,
  },
  voiceButton: {
    padding: 12,
    borderRadius: 20,
    marginRight: 8,
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
