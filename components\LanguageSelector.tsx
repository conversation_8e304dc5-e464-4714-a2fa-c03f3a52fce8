import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useFuturisticTheme } from '@/hooks/useFuturisticTheme';
import { TranslationService } from '@/services/TranslationService';
import React, { useEffect, useState } from 'react';
import { FlatList, Modal, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface Language {
  code: string;
  name: string;
}

interface LanguageSelectorProps {
  selectedLanguage: string;
  onLanguageSelect: (language: string) => void;
}

export default function LanguageSelector({ selectedLanguage, onLanguageSelect }: LanguageSelectorProps) {
  const { colors } = useFuturisticTheme();
  const [languages, setLanguages] = useState<Language[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedLanguageName, setSelectedLanguageName] = useState('English');

  useEffect(() => {
    loadLanguages();
  }, []);

  useEffect(() => {
    updateSelectedLanguageName();
  }, [selectedLanguage]);

  const loadLanguages = async () => {
    try {
      const supportedLanguages = await TranslationService.getSupportedLanguages();
      setLanguages(supportedLanguages);
    } catch (error) {
      console.log('Error loading languages:', error);
    }
  };

  const updateSelectedLanguageName = async () => {
    try {
      const name = await TranslationService.getLanguageName(selectedLanguage);
      setSelectedLanguageName(name);
    } catch (error) {
      setSelectedLanguageName('English');
    }
  };

  const handleLanguageSelect = (language: Language) => {
    onLanguageSelect(language.code);
    setModalVisible(false);
  };

  const renderLanguage = ({ item }: { item: Language }) => (
    <TouchableOpacity
      style={[styles.languageItem, {
        backgroundColor: item.code === selectedLanguage ? colors.primary : colors.surface,
        borderBottomColor: colors.border
      }]}
      onPress={() => handleLanguageSelect(item)}
    >
      <ThemedText style={{
        color: item.code === selectedLanguage ? 'white' : colors.text,
        fontWeight: item.code === selectedLanguage ? 'bold' : 'normal'
      }}>
        {item.name}
      </ThemedText>
      {item.code === selectedLanguage && (
        <IconSymbol name="checkmark" size={20} color="white" />
      )}
    </TouchableOpacity>
  );

  return (
    <>
      <TouchableOpacity
        style={[styles.selector, {
          backgroundColor: colors.surface,
          borderColor: colors.border
        }]}
        onPress={() => setModalVisible(true)}
      >
        <ThemedText style={{ color: colors.text }}>{selectedLanguageName}</ThemedText>
        <IconSymbol name="chevron.down" size={16} color={colors.textSecondary} />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <SafeAreaView style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <View style={styles.modalHeader}>
              <ThemedText type="subtitle" style={{ color: colors.text }}>Select Language</ThemedText>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <IconSymbol name="xmark" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            <FlatList
              data={languages}
              renderItem={renderLanguage}
              keyExtractor={(item) => item.code}
              style={styles.languageList}
            />
          </SafeAreaView>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  selector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    maxHeight: '70%',
    borderRadius: 12,
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  languageList: {
    maxHeight: 400,
  },
  languageItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
});