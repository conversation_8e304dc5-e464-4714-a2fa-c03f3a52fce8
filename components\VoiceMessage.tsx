import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

interface VoiceMessageProps {
  duration: number;
  isOwn: boolean;
  onPlay?: () => void;
}

export default function VoiceMessage({ duration, isOwn, onPlay }: VoiceMessageProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [isPlaying, setIsPlaying] = useState(false);

  const handlePlay = () => {
    setIsPlaying(!isPlaying);
    onPlay?.();
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <View style={[
      styles.container,
      { backgroundColor: isOwn ? colors.tint : colors.text + '10' }
    ]}>
      <TouchableOpacity style={styles.playButton} onPress={handlePlay}>
        <IconSymbol 
          name={isPlaying ? 'pause.fill' : 'play.fill'} 
          size={20} 
          color={isOwn ? 'white' : colors.text} 
        />
      </TouchableOpacity>
      
      <View style={styles.waveform}>
        {[...Array(12)].map((_, i) => (
          <View 
            key={i}
            style={[
              styles.waveBar,
              { 
                height: Math.random() * 20 + 10,
                backgroundColor: isOwn ? 'white' : colors.text,
                opacity: isPlaying && i < 6 ? 1 : 0.3
              }
            ]}
          />
        ))}
      </View>
      
      <ThemedText style={[
        styles.duration,
        { color: isOwn ? 'white' : colors.text }
      ]}>
        {formatDuration(duration)}
      </ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 16,
    minWidth: 200,
  },
  playButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  waveform: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    height: 30,
    marginRight: 12,
  },
  waveBar: {
    width: 3,
    marginHorizontal: 1,
    borderRadius: 1.5,
  },
  duration: {
    fontSize: 12,
    fontWeight: '500',
  },
});