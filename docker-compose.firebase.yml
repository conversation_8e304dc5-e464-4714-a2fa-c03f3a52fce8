version: '3.8'

services:
  # Firebase Emulator Suite for local development
  firebase-emulators:
    image: node:18-alpine
    container_name: voxchat_firebase_emulators
    working_dir: /app
    ports:
      - "4000:4000"   # Emulator Suite UI
      - "9099:9099"   # Authentication Emulator
      - "8080:8080"   # Firestore Emulator
      - "9199:9199"   # Storage Emulator
      - "5001:5001"   # Functions Emulator
    volumes:
      - ./firebase:/app
      - firebase_data:/app/data
    environment:
      - FIREBASE_TOKEN=${FIREBASE_TOKEN:-}
    command: >
      sh -c "
        npm install -g firebase-tools &&
        firebase emulators:start --host 0.0.0.0 --import ./data --export-on-exit ./data
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend service for enhanced translation (optional)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.firebase
    container_name: voxchat_backend_firebase
    restart: unless-stopped
    environment:
      # Firebase Admin SDK configuration
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID:-voxchat-dev}
      FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY:-}
      FIREBASE_CLIENT_EMAIL: ${FIREBASE_CLIENT_EMAIL:-}
      
      # Translation service
      GOOGLE_TRANSLATE_API_KEY: ${GOOGLE_TRANSLATE_API_KEY:-}
      
      # Development settings
      FIREBASE_EMULATOR_HOST: firebase-emulators
      FIRESTORE_EMULATOR_HOST: firebase-emulators:8080
    ports:
      - "8000:8000"
    depends_on:
      firebase-emulators:
        condition: service_healthy
    volumes:
      - ./backend:/app
    command: python -m app.firebase_main

  # Frontend development server (optional)
  frontend:
    image: node:18-alpine
    container_name: voxchat_frontend
    working_dir: /app
    ports:
      - "8081:8081"   # Expo Dev Server
      - "19000:19000" # Expo Dev Tools
      - "19001:19001" # Expo Tunnel
      - "19002:19002" # Expo LAN
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0
      - REACT_NATIVE_PACKAGER_HOSTNAME=0.0.0.0
    command: >
      sh -c "
        npm install &&
        npx expo start --web --host 0.0.0.0
      "
    depends_on:
      - firebase-emulators

volumes:
  firebase_data:
    driver: local

networks:
  default:
    name: voxchat_firebase_network
