import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useState } from 'react';
import { FlatList, KeyboardAvoidingView, Platform, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface Message {
  id: string;
  text: string;
  isOwn: boolean;
  timestamp: Date;
  translated?: string;
  showOriginal?: boolean;
}



export default function ChatScreen({ contactName = 'Contact' }: { contactName?: string }) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');

  const sendMessage = () => {
    if (inputText.trim()) {
      const newMessage: Message = {
        id: Date.now().toString(),
        text: inputText,
        isOwn: true,
        timestamp: new Date(),
      };
      setMessages([...messages, newMessage]);
      setInputText('');
    }
  };

  const toggleTranslation = (messageId: string) => {
    setMessages(messages.map(msg =>
      msg.id === messageId ? { ...msg, showOriginal: !msg.showOriginal } : msg
    ));
  };

  const renderMessage = ({ item }: { item: Message }) => (
    <View style={[styles.messageContainer, item.isOwn ? styles.ownMessage : styles.otherMessage]}>
      <View style={[
        styles.messageBubble,
        { backgroundColor: item.isOwn ? colors.tint : colors.text + '10' }
      ]}>
        <ThemedText style={[
          styles.messageText,
          { color: item.isOwn ? 'white' : colors.text }
        ]}>
          {item.showOriginal ? item.text : (item.translated || item.text)}
        </ThemedText>
        {item.translated && (
          <TouchableOpacity
            style={styles.translateButton}
            onPress={() => toggleTranslation(item.id)}
          >
            <ThemedText style={[
              styles.translateText,
              { color: item.isOwn ? 'white' : colors.tint }
            ]}>
              {item.showOriginal ? 'Show Translation' : 'See Original'}
            </ThemedText>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <ThemedText style={[styles.emptyText, { color: colors.text + '60' }]}>
        No messages yet. Start the conversation!
      </ThemedText>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ThemedView style={styles.content}>
        <View style={[styles.header, { borderBottomColor: colors.text + '20' }]}>
          <TouchableOpacity>
            <IconSymbol name="chevron.left" size={24} color={colors.text} />
          </TouchableOpacity>
          <ThemedText type="defaultSemiBold">{contactName}</ThemedText>
          <TouchableOpacity>
            <IconSymbol name="phone" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>

        <FlatList
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={[
            styles.messagesContent,
            messages.length === 0 && styles.emptyMessagesContent
          ]}
          ListEmptyComponent={renderEmptyState}
        />

        <View style={[styles.inputContainer, { borderTopColor: colors.text + '20' }]}>
          <TextInput
            style={[styles.textInput, {
              backgroundColor: colors.text + '10',
              color: colors.text
            }]}
            value={inputText}
            onChangeText={setInputText}
            placeholder="Type a message..."
            placeholderTextColor={colors.text + '60'}
            multiline
          />
          <TouchableOpacity style={styles.voiceButton}>
            <IconSymbol name="mic" size={20} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.sendButton, { backgroundColor: colors.tint }]}
            onPress={sendMessage}
          >
            <IconSymbol name="paperplane.fill" size={20} color="white" />
          </TouchableOpacity>
        </View>
        </ThemedView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50,
    borderBottomWidth: 1,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
  },
  emptyMessagesContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  messageContainer: {
    marginVertical: 4,
  },
  ownMessage: {
    alignItems: 'flex-end',
  },
  otherMessage: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
  },
  messageText: {
    fontSize: 16,
  },
  translateButton: {
    marginTop: 4,
  },
  translateText: {
    fontSize: 12,
    textDecorationLine: 'underline',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
  },
  textInput: {
    flex: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 8,
    maxHeight: 100,
  },
  voiceButton: {
    padding: 12,
    marginRight: 8,
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
});