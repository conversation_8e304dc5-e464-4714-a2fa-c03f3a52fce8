import { FirebaseAuthService } from '@/services/FirebaseAuthService';
import { useState } from 'react';
import { Alert, Button, View } from 'react-native';
import OTPScreen from './OTPScreen';

const PhoneSignIn = () => {
  const [confirm, setConfirm] = useState(null);
  const [loading, setLoading] = useState(false);

  async function signInWithPhoneNumber(phoneNumber) {
    setLoading(true);
    try {
      const confirmation = await FirebaseAuthService.signInWithPhoneNumber(phoneNumber);
      setConfirm(confirmation);
    } catch (error) {
      console.error('Phone sign in error:', error);
      Alert.alert('Error', error.message || 'Failed to send OTP');
    } finally {
      setLoading(false);
    }
  }

  return (
    <View>
      {confirm ? (
        <OTPScreen confirm={confirm} />
      ) : (
        <Button
          title={loading ? "Sending OTP..." : "Phone Number Sign In"}
          onPress={() => signInWithPhoneNumber('******-555-3434')}
          disabled={loading}
        />
      )}
    </View>
  );
};

export default PhoneSignIn;