import AsyncStorage from '@react-native-async-storage/async-storage';

const THEME_KEY = 'user_theme';

export class StorageService {
  static async getTheme(): Promise<string> {
    try {
      const theme = await AsyncStorage.getItem(THEME_KEY);
      return theme || 'white';
    } catch (error) {
      console.error('Error getting theme:', error);
      return 'white';
    }
  }

  static async setTheme(theme: string): Promise<void> {
    try {
      await AsyncStorage.setItem(THEME_KEY, theme);
    } catch (error) {
      console.error('Error setting theme:', error);
    }
  }
}