import {
    doc,
    getDoc,
    serverTimestamp,
    setDoc,
    Timestamp
} from 'firebase/firestore';
import { db } from './FirebaseService';

export interface TranslationResult {
  original: string;
  translated: string;
  sourceLanguage: string;
  targetLanguage: string;
  needsTranslation: boolean;
  confidence?: string;
  error?: string;
}

export interface CachedTranslation {
  id: string;
  sourceText: string;
  sourceLanguage: string;
  targetLanguage: string;
  translatedText: string;
  confidence: string;
  provider: string;
  createdAt: Timestamp;
  expiresAt: Timestamp;
}

export class FirebaseTranslationService {
  private static readonly CACHE_DURATION_DAYS = 30;
  private static readonly BACKEND_URL = process.env.EXPO_PUBLIC_BACKEND_URL || 'http://localhost:8000';

  static async translateText(text: string, targetLanguage: string = 'en'): Promise<TranslationResult> {
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return {
        original: text,
        translated: text,
        sourceLanguage: 'unknown',
        targetLanguage,
        needsTranslation: false,
        error: 'Empty text provided'
      };
    }

    try {
      // Check cache first
      const cachedResult = await this.getCachedTranslation(text, targetLanguage);
      if (cachedResult) {
        return {
          original: text,
          translated: cachedResult.translatedText,
          sourceLanguage: cachedResult.sourceLanguage,
          targetLanguage,
          needsTranslation: true,
          confidence: cachedResult.confidence
        };
      }

      // Call backend translation service
      const response = await fetch(`${this.BACKEND_URL}/messages/translate`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ text: text.trim(), target_lang: targetLanguage }),
        signal: AbortSignal.timeout(10000) // 10 second timeout
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();

      // Cache the result if successful
      if (result.translated && result.translated !== text) {
        await this.cacheTranslation(
          text,
          result.source_language || 'auto',
          targetLanguage,
          result.translated,
          result.confidence || 'medium'
        );
      }

      return {
        original: text,
        translated: result.translated || text,
        sourceLanguage: result.source_language || 'unknown',
        targetLanguage,
        needsTranslation: result.needs_translation !== false,
        confidence: result.confidence,
        error: result.error
      };

    } catch (error) {
      console.error('Translation error:', error);
      
      // Return error result without fallback
      return {
        original: text,
        translated: text, // Return original text if translation fails
        sourceLanguage: 'unknown',
        targetLanguage,
        needsTranslation: false,
        confidence: 'low',
        error: error instanceof Error ? error.message : 'Translation service unavailable'
      };
    }
  }

  static async detectLanguage(text: string): Promise<{
    languageCode: string;
    languageName: string;
    confidence: string;
    supported: boolean;
  }> {
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return {
        languageCode: 'en',
        languageName: 'English',
        confidence: 'low',
        supported: true
      };
    }

    try {
      const response = await fetch(`${this.BACKEND_URL}/messages/detect-language`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ text: text.trim() }),
        signal: AbortSignal.timeout(5000)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Language detection error:', error);
      
      // Fallback to pattern-based detection
      return this.detectLanguageByPattern(text);
    }
  }

  static async getSupportedLanguages(): Promise<Record<string, string>> {
    try {
      const response = await fetch(`${this.BACKEND_URL}/messages/languages`, {
        method: 'GET',
        headers: { 'Accept': 'application/json' },
        signal: AbortSignal.timeout(5000)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching supported languages:', error);
      return this.getDefaultLanguages();
    }
  }

  static async batchTranslate(texts: string[], targetLanguage: string = 'en'): Promise<TranslationResult[]> {
    if (!texts || texts.length === 0) {
      return [];
    }

    const results: TranslationResult[] = [];
    
    // Process in batches of 10 to avoid overwhelming the service
    const batchSize = 10;
    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize);
      const batchPromises = batch.map(text => this.translateText(text, targetLanguage));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  }

  // Cache management
  private static async getCachedTranslation(
    sourceText: string,
    targetLanguage: string
  ): Promise<CachedTranslation | null> {
    try {
      const cacheId = this.generateCacheId(sourceText, targetLanguage);
      const cacheDoc = await getDoc(doc(db, 'translationCache', cacheId));
      
      if (cacheDoc.exists()) {
        const cachedData = cacheDoc.data() as CachedTranslation;
        
        // Check if cache is still valid
        const now = new Date();
        const expiresAt = cachedData.expiresAt.toDate();
        
        if (now < expiresAt) {
          return cachedData;
        } else {
          // Cache expired, could delete it here
          // await deleteDoc(doc(db, 'translationCache', cacheId));
        }
      }
      
      return null;
    } catch (error) {
      console.error('Get cached translation error:', error);
      return null;
    }
  }

  private static async cacheTranslation(
    sourceText: string,
    sourceLanguage: string,
    targetLanguage: string,
    translatedText: string,
    confidence: string
  ): Promise<void> {
    try {
      const cacheId = this.generateCacheId(sourceText, targetLanguage);
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + this.CACHE_DURATION_DAYS);

      const cacheData: Omit<CachedTranslation, 'id'> = {
        sourceText,
        sourceLanguage,
        targetLanguage,
        translatedText,
        confidence,
        provider: 'google',
        createdAt: serverTimestamp() as Timestamp,
        expiresAt: Timestamp.fromDate(expiresAt)
      };

      await setDoc(doc(db, 'translationCache', cacheId), cacheData);
    } catch (error) {
      console.error('Cache translation error:', error);
      // Don't throw error, caching failure shouldn't break translation
    }
  }

  private static generateCacheId(sourceText: string, targetLanguage: string): string {
    // Create a simple hash for the cache ID
    const text = sourceText.toLowerCase().trim() + '_' + targetLanguage;
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }



  private static detectLanguageByPattern(text: string): {
    languageCode: string;
    languageName: string;
    confidence: string;
    supported: boolean;
  } {
    const languagePatterns: Record<string, string> = {
      "bonjour": "fr", "salut": "fr", "merci": "fr",
      "hola": "es", "gracias": "es", "buenos": "es",
      "hallo": "de", "danke": "de", "guten": "de",
      "ciao": "it", "grazie": "it", "buongiorno": "it",
      "olá": "pt", "obrigado": "pt", "bom": "pt",
      "привет": "ru", "спасибо": "ru", "доброе": "ru",
      "こんにちは": "ja", "ありがとう": "ja", "おはよう": "ja",
      "안녕": "ko", "감사": "ko", "좋은": "ko",
      "你好": "zh", "谢谢": "zh", "早上": "zh",
      "hello": "en", "thank": "en", "good": "en"
    };

    const textLower = text.toLowerCase();
    let detectedLang = "en";
    let confidence = "medium";

    for (const [pattern, lang] of Object.entries(languagePatterns)) {
      if (textLower.includes(pattern)) {
        detectedLang = lang;
        confidence = "high";
        break;
      }
    }

    const languageNames = this.getDefaultLanguages();

    return {
      languageCode: detectedLang,
      languageName: languageNames[detectedLang] || "Unknown",
      confidence,
      supported: true
    };
  }

  private static getDefaultLanguages(): Record<string, string> {
    return {
      "en": "English", "es": "Spanish", "fr": "French", "de": "German",
      "it": "Italian", "pt": "Portuguese", "ru": "Russian",
      "ja": "Japanese", "ko": "Korean", "zh": "Chinese",
      "ar": "Arabic", "hi": "Hindi", "tr": "Turkish", "nl": "Dutch",
      "sv": "Swedish", "no": "Norwegian", "da": "Danish", "fi": "Finnish"
    };
  }
}
