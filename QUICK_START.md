# 🚀 voXchat Quick Start Guide

This guide will help you get voXchat up and running with Firebase real-time messaging, translation, and voice features in just 5 minutes.

## 📋 Prerequisites

- **Node.js** (v18 or higher)
- **Expo CLI** (for mobile development)
- **Google Account** (for Firebase Console)

## 🏃‍♂️ Quick Setup

### 1. <PERSON>lone and Install

```bash
# Clone the repository
git clone https://github.com/Raman247365/voXchat.git
cd voXchat

# Install dependencies
npm install
```

### 2. Firebase Setup

```bash
# Install Firebase
npm install firebase

# Run setup script (Windows)
setup-firebase.bat

# Or install manually
# See FIREBASE_SETUP.md for detailed instructions
```

### 3. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your Firebase credentials
# Get these from Firebase Console > Project Settings
```

### 4. Start the App

```bash
# Start Expo development server
npx expo start
```

## 🎯 Firebase Features

### ✅ Real-time Messaging
- **Firestore Database**: Real-time message synchronization
- **Typing Indicators**: See when someone is typing
- **Delivery Receipts**: Know when messages are delivered and read
- **Offline Support**: Messages sync when back online

### ✅ Authentication
- **Firebase Auth**: Secure user authentication
- **User Profiles**: Complete user management
- **Session Management**: Automatic login persistence

### ✅ Translation Integration
- **Google Translate**: Real translation with caching
- **Language Detection**: Automatic language detection
- **Firestore Caching**: Cached translations in database
- **Offline Fallbacks**: Works without internet

### ✅ Voice Features
- **Firebase Storage**: Voice message storage with CDN
- **Voice Recording**: Record voice messages with visual feedback
- **Voice Playback**: Play voice messages with controls
- **Cross-platform**: Works on iOS, Android, and Web

## 🔧 Architecture Overview

```
Frontend (React Native/Expo)
├── WebSocketService - Real-time messaging
├── EnhancedVoiceService - Voice recording/playback
├── BackendTranslationService - Translation API
└── ChatScreen - Enhanced UI with all features

Backend (Python FastAPI)
├── WebSocket Handler - Real-time message routing
├── Translation API - Language detection & translation
├── Message Storage - In-memory message database
└── Connection Management - User status tracking
```

## 🧪 Testing the Features

### Real-time Messaging
1. Open the chat screen
2. Check connection status (should show "Connected")
3. Type a message - you'll see typing indicators
4. Send messages - they appear instantly

### Translation
1. Send a message in any language
2. The system detects the language automatically
3. Translation happens in real-time
4. Toggle between original and translated text

### Voice Recording
1. Tap and hold the microphone button
2. Record your voice message
3. Release to stop recording
4. Voice message appears with playback controls

## 🐛 Troubleshooting

### Backend Issues
```bash
# Check if backend is running
curl http://localhost:8000/health

# View backend logs
cd backend
python run_simple.py
```

### Frontend Issues
```bash
# Clear cache and restart
npx expo start --clear

# Check WebSocket connection in browser console
# Look for "WebSocket connected" messages
```

### Common Problems

1. **"WebSocket connection failed"**
   - Make sure backend is running on port 8000
   - Check firewall settings

2. **"Translation service unavailable"**
   - Backend should be running
   - Check network connectivity

3. **"Voice recording failed"**
   - Grant microphone permissions
   - Check device audio settings

## 📱 Mobile Testing

### Using Expo Go
1. Install Expo Go app on your phone
2. Scan the QR code from `npx expo start`
3. Test all features on real device

### Using Simulator
```bash
# iOS Simulator
npx expo start --ios

# Android Emulator
npx expo start --android
```

## 🔗 API Endpoints

- `GET /health` - Health check
- `POST /messages/translate` - Translate text
- `POST /messages/detect-language` - Detect language
- `GET /messages/languages` - Supported languages
- `WS /ws/{user_id}` - WebSocket connection

## 📚 Next Steps

1. **Test all features** using the chat interface
2. **Check real-time messaging** with multiple browser tabs
3. **Try voice recording** on mobile device
4. **Test translation** with different languages
5. **Monitor connection status** by stopping/starting backend

## 🎉 Success Indicators

- ✅ Backend starts without errors
- ✅ Frontend connects to WebSocket
- ✅ Messages send and receive instantly
- ✅ Typing indicators work
- ✅ Translation API responds
- ✅ Voice recording works on mobile
- ✅ Connection status updates correctly

## 🆘 Need Help?

1. Check the console logs for error messages
2. Verify all services are running
3. Test individual API endpoints
4. Check network connectivity
5. Review the implementation in `DEVELOPMENT.md`

Happy chatting! 🎊
