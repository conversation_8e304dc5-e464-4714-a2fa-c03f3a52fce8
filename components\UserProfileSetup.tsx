import React, { useState } from 'react';
import { StyleSheet, TextInput, TouchableOpacity, View, ScrollView } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useFuturisticTheme } from '@/hooks/useFuturisticTheme';
import LanguageSelector from '@/components/LanguageSelector';
import { User } from '@/models/User';

interface UserProfileSetupProps {
  onComplete: (userData: Partial<User>) => void;
}

export default function UserProfileSetup({ onComplete }: UserProfileSetupProps) {
  const { colors } = useFuturisticTheme();
  const [step, setStep] = useState(1);
  const [userData, setUserData] = useState<Partial<User>>({
    primaryLanguage: 'en',
    secondaryLanguages: [],
    autoTranslate: true,
    voiceEnabled: true,
    signLanguageEnabled: false,
    fontSize: 'medium',
    allowVoiceMessages: true,
    readReceiptsEnabled: true,
    profileVisibility: 'friends',
    status: 'online'
  });

  const updateUserData = (field: keyof User, value: any) => {
    setUserData(prev => ({ ...prev, [field]: value }));
  };

  const nextStep = () => {
    if (step < 4) setStep(step + 1);
    else onComplete(userData);
  };

  const prevStep = () => {
    if (step > 1) setStep(step - 1);
  };

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <ThemedText type="title" style={[styles.stepTitle, { color: colors.text }]}>
        Basic Information
      </ThemedText>
      
      <TextInput
        style={[styles.input, { backgroundColor: colors.surface, color: colors.text, borderColor: colors.border }]}
        placeholder="Display Name"
        placeholderTextColor={colors.textSecondary}
        value={userData.displayName || ''}
        onChangeText={(text) => updateUserData('displayName', text)}
      />
      
      <TextInput
        style={[styles.input, { backgroundColor: colors.surface, color: colors.text, borderColor: colors.border }]}
        placeholder="Username"
        placeholderTextColor={colors.textSecondary}
        value={userData.username || ''}
        onChangeText={(text) => updateUserData('username', text)}
        autoCapitalize="none"
      />
      
      <TextInput
        style={[styles.input, { backgroundColor: colors.surface, color: colors.text, borderColor: colors.border }]}
        placeholder="Phone Number (Optional)"
        placeholderTextColor={colors.textSecondary}
        value={userData.phoneNumber || ''}
        onChangeText={(text) => updateUserData('phoneNumber', text)}
        keyboardType="phone-pad"
      />
      
      <View style={styles.genderContainer}>
        <ThemedText style={[styles.label, { color: colors.text }]}>Gender (Optional)</ThemedText>
        {['male', 'female', 'other', 'prefer_not_to_say'].map((gender) => (
          <TouchableOpacity
            key={gender}
            style={[styles.radioOption, { 
              backgroundColor: userData.gender === gender ? colors.primary : colors.surface,
              borderColor: colors.border 
            }]}
            onPress={() => updateUserData('gender', gender)}
          >
            <ThemedText style={{ 
              color: userData.gender === gender ? 'white' : colors.text,
              textTransform: 'capitalize'
            }}>
              {gender.replace('_', ' ')}
            </ThemedText>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      <ThemedText type="title" style={[styles.stepTitle, { color: colors.text }]}>
        Language Preferences
      </ThemedText>
      
      <View style={styles.fieldContainer}>
        <ThemedText style={[styles.label, { color: colors.text }]}>Primary Language</ThemedText>
        <LanguageSelector
          selectedLanguage={userData.primaryLanguage || 'en'}
          onLanguageSelect={(lang) => updateUserData('primaryLanguage', lang)}
        />
      </View>
      
      <View style={styles.switchContainer}>
        <View style={styles.switchRow}>
          <View>
            <ThemedText style={[styles.switchLabel, { color: colors.text }]}>Auto-translate messages</ThemedText>
            <ThemedText style={[styles.switchSubtitle, { color: colors.textSecondary }]}>
              Automatically translate incoming messages
            </ThemedText>
          </View>
          <TouchableOpacity
            style={[styles.switch, { backgroundColor: userData.autoTranslate ? colors.primary : colors.border }]}
            onPress={() => updateUserData('autoTranslate', !userData.autoTranslate)}
          >
            <View style={[styles.switchThumb, { 
              transform: [{ translateX: userData.autoTranslate ? 20 : 2 }],
              backgroundColor: 'white'
            }]} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderStep3 = () => (
    <View style={styles.stepContainer}>
      <ThemedText type="title" style={[styles.stepTitle, { color: colors.text }]}>
        Accessibility Features
      </ThemedText>
      
      {[
        { key: 'voiceEnabled', label: 'Voice Features', subtitle: 'Text-to-speech and voice messages' },
        { key: 'signLanguageEnabled', label: 'Sign Language Support', subtitle: 'Camera-based gesture recognition' },
        { key: 'allowVoiceMessages', label: 'Allow Voice Messages', subtitle: 'Receive audio messages from contacts' },
        { key: 'readReceiptsEnabled', label: 'Read Receipts', subtitle: 'Show when you\'ve read messages' }
      ].map((item) => (
        <View key={item.key} style={styles.switchContainer}>
          <View style={styles.switchRow}>
            <View style={{ flex: 1 }}>
              <ThemedText style={[styles.switchLabel, { color: colors.text }]}>{item.label}</ThemedText>
              <ThemedText style={[styles.switchSubtitle, { color: colors.textSecondary }]}>
                {item.subtitle}
              </ThemedText>
            </View>
            <TouchableOpacity
              style={[styles.switch, { 
                backgroundColor: userData[item.key as keyof User] ? colors.primary : colors.border 
              }]}
              onPress={() => updateUserData(item.key as keyof User, !userData[item.key as keyof User])}
            >
              <View style={[styles.switchThumb, { 
                transform: [{ translateX: userData[item.key as keyof User] ? 20 : 2 }],
                backgroundColor: 'white'
              }]} />
            </TouchableOpacity>
          </View>
        </View>
      ))}
      
      <View style={styles.fieldContainer}>
        <ThemedText style={[styles.label, { color: colors.text }]}>Font Size</ThemedText>
        <View style={styles.fontSizeContainer}>
          {['small', 'medium', 'large'].map((size) => (
            <TouchableOpacity
              key={size}
              style={[styles.fontSizeOption, { 
                backgroundColor: userData.fontSize === size ? colors.primary : colors.surface,
                borderColor: colors.border 
              }]}
              onPress={() => updateUserData('fontSize', size)}
            >
              <ThemedText style={{ 
                color: userData.fontSize === size ? 'white' : colors.text,
                textTransform: 'capitalize'
              }}>
                {size}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  const renderStep4 = () => (
    <View style={styles.stepContainer}>
      <ThemedText type="title" style={[styles.stepTitle, { color: colors.text }]}>
        Privacy Settings
      </ThemedText>
      
      <View style={styles.fieldContainer}>
        <ThemedText style={[styles.label, { color: colors.text }]}>Profile Visibility</ThemedText>
        <View style={styles.privacyContainer}>
          {[
            { key: 'public', label: 'Public', subtitle: 'Anyone can see your profile' },
            { key: 'friends', label: 'Friends Only', subtitle: 'Only your contacts can see your profile' },
            { key: 'private', label: 'Private', subtitle: 'Profile is hidden from everyone' }
          ].map((option) => (
            <TouchableOpacity
              key={option.key}
              style={[styles.privacyOption, { 
                backgroundColor: userData.profileVisibility === option.key ? colors.primary : colors.surface,
                borderColor: colors.border 
              }]}
              onPress={() => updateUserData('profileVisibility', option.key)}
            >
              <ThemedText style={{ 
                color: userData.profileVisibility === option.key ? 'white' : colors.text,
                fontWeight: 'bold'
              }}>
                {option.label}
              </ThemedText>
              <ThemedText style={{ 
                color: userData.profileVisibility === option.key ? 'white' : colors.textSecondary,
                fontSize: 12,
                marginTop: 4
              }}>
                {option.subtitle}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <ThemedText type="title" style={{ color: colors.text }}>Complete Your Profile</ThemedText>
        <ThemedText style={[styles.stepIndicator, { color: colors.textSecondary }]}>
          Step {step} of 4
        </ThemedText>
      </View>

      <View style={styles.progressBar}>
        <View style={[styles.progressFill, { 
          width: `${(step / 4) * 100}%`,
          backgroundColor: colors.primary 
        }]} />
      </View>

      {step === 1 && renderStep1()}
      {step === 2 && renderStep2()}
      {step === 3 && renderStep3()}
      {step === 4 && renderStep4()}

      <View style={styles.buttonContainer}>
        {step > 1 && (
          <TouchableOpacity 
            style={[styles.button, styles.backButton, { borderColor: colors.border }]}
            onPress={prevStep}
          >
            <ThemedText style={{ color: colors.text }}>Back</ThemedText>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity 
          style={[styles.button, styles.nextButton, { backgroundColor: colors.primary }]}
          onPress={nextStep}
        >
          <ThemedText style={{ color: 'white', fontWeight: 'bold' }}>
            {step === 4 ? 'Complete Setup' : 'Next'}
          </ThemedText>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  header: { padding: 20, alignItems: 'center' },
  stepIndicator: { marginTop: 8, fontSize: 14 },
  progressBar: { height: 4, backgroundColor: '#E5E5E5', marginHorizontal: 20 },
  progressFill: { height: '100%', borderRadius: 2 },
  stepContainer: { padding: 20 },
  stepTitle: { marginBottom: 24, textAlign: 'center' },
  input: { height: 50, borderWidth: 1, borderRadius: 12, paddingHorizontal: 16, marginBottom: 16 },
  label: { fontSize: 16, fontWeight: '600', marginBottom: 8 },
  fieldContainer: { marginBottom: 24 },
  genderContainer: { marginTop: 16 },
  radioOption: { padding: 12, borderRadius: 8, borderWidth: 1, marginBottom: 8 },
  switchContainer: { marginBottom: 20 },
  switchRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
  switchLabel: { fontSize: 16, fontWeight: '600' },
  switchSubtitle: { fontSize: 14, marginTop: 2 },
  switch: { width: 44, height: 24, borderRadius: 12, justifyContent: 'center' },
  switchThumb: { width: 20, height: 20, borderRadius: 10 },
  fontSizeContainer: { flexDirection: 'row', gap: 12 },
  fontSizeOption: { flex: 1, padding: 12, borderRadius: 8, borderWidth: 1, alignItems: 'center' },
  privacyContainer: { gap: 12 },
  privacyOption: { padding: 16, borderRadius: 12, borderWidth: 1 },
  buttonContainer: { flexDirection: 'row', padding: 20, gap: 12 },
  button: { flex: 1, height: 50, borderRadius: 12, justifyContent: 'center', alignItems: 'center' },
  backButton: { borderWidth: 1 },
  nextButton: {},
});