import { useFuturisticTheme } from '@/hooks/useFuturisticTheme';
import React, { useRef } from 'react';
import {
    Animated,
    Easing,
    Pressable,
    StyleSheet,
    ViewStyle
} from 'react-native';

interface FuturisticCardProps {
  children: React.ReactNode;
  variant?: 'default' | 'glass' | 'elevated' | 'neon' | 'cyber' | 'hologram';
  padding?: number;
  style?: ViewStyle;
  onPress?: () => void;
  animated?: boolean;
  glowEffect?: boolean;
}

export default function FuturisticCard({
  children,
  variant = 'default',
  padding = 16,
  style,
  onPress,
  animated = true,
  glowEffect = false
}: FuturisticCardProps) {
  const { colors } = useFuturisticTheme();

  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;
  const borderAnim = useRef(new Animated.Value(0)).current;

  // Animation handlers
  const handlePressIn = (event: any) => {
    if (!animated || !onPress) return;

    // Get touch position for ripple effect
    const { locationX, locationY } = event.nativeEvent;

    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.98,
        duration: 100,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
      Animated.timing(glowAnim, {
        toValue: 1,
        duration: 100,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    if (!animated || !onPress) return;

    Animated.spring(scaleAnim, {
      toValue: 1,
      tension: 300,
      friction: 10,
      useNativeDriver: true,
    }).start();

    Animated.timing(glowAnim, {
      toValue: 0,
      duration: 200,
      easing: Easing.out(Easing.ease),
      useNativeDriver: true,
    }).start();
  };

  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: 16,
      padding,
      overflow: 'hidden',
    };

    const variantStyles = {
      default: {
        backgroundColor: colors.surface,
        borderWidth: 1,
        borderColor: colors.border,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 3,
      },
      glass: {
        backgroundColor: colors.glass,
        borderWidth: 1,
        borderColor: colors.borderHover,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
        elevation: 5,
      },
      elevated: {
        backgroundColor: colors.surface,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.2,
        shadowRadius: 16,
        elevation: 8,
      },
      neon: {
        backgroundColor: colors.surface,
        borderWidth: 2,
        borderColor: colors.neon,
        shadowColor: colors.neon,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 12,
        elevation: 6,
      },
      cyber: {
        backgroundColor: colors.surface,
        borderWidth: 2,
        borderColor: colors.cyber,
        shadowColor: colors.cyber,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 12,
        elevation: 6,
      },
      hologram: {
        backgroundColor: colors.hologram,
        borderWidth: 1,
        borderColor: colors.cyber,
        shadowColor: colors.cyber,
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.25,
        shadowRadius: 14,
        elevation: 7,
      },
    };

    return {
      ...baseStyle,
      ...variantStyles[variant],
    };
  };

  const glowOpacity = glowAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.3],
  });

  const CardContent = (
    <Animated.View
      style={[
        getCardStyle(),
        style,
        animated && {
          transform: [{ scale: scaleAnim }],
        }
      ]}
    >
      {/* Glow effect overlay */}
      {glowEffect && (
        <Animated.View
          style={[
            StyleSheet.absoluteFill,
            {
              borderRadius: 16,
              backgroundColor: colors.glow,
              opacity: glowOpacity,
            }
          ]}
        />
      )}

      {children}
    </Animated.View>
  );

  if (onPress) {
    return (
      <Pressable
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        {CardContent}
      </Pressable>
    );
  }

  return CardContent;
}