/**
 * voXchat Design System - Cutting-Edge Minimal UI
 * Based on ultra-minimal, geometric precision design principles
 */

// Spacing System - 8px base unit
export const Spacing = {
  xs: 8,
  sm: 16,
  md: 24,
  lg: 32,
  xl: 48,
  xxl: 64,
  xxxl: 96,
  xxxxl: 128,
} as const;

// Typography System
export const Typography = {
  fontFamily: {
    primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    monospace: '"SF Mono", Monaco, "Cascadia Code", monospace',
  },
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    bold: '600',
  },
  fontSize: {
    display: 48,
    h1: 36,
    h2: 28,
    h3: 24,
    h4: 20,
    bodyLarge: 18,
    body: 16,
    bodySmall: 14,
    caption: 12,
  },
  lineHeight: {
    display: 56,
    h1: 44,
    h2: 36,
    h3: 32,
    h4: 28,
    bodyLarge: 28,
    body: 24,
    bodySmall: 20,
    caption: 16,
  },
  letterSpacing: {
    display: 2,
    h1: 1.5,
    h2: 1,
    h3: 0.5,
    h4: 0.3,
    body: 0,
    button: 0.3,
    caption: 0.2,
  },
} as const;

// Component Sizing
export const ComponentSize = {
  button: {
    small: { height: 32, paddingHorizontal: 12 },
    medium: { height: 44, paddingHorizontal: 16 },
    large: { height: 56, paddingHorizontal: 20 },
  },
  input: {
    height: 44,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  icon: {
    small: 16,
    medium: 24,
    large: 32,
    xlarge: 48,
  },
} as const;

// Border Radius - Sharp, cutting-edge design
export const BorderRadius = {
  none: 0,
  small: 2,
  medium: 4,
  large: 8,
  circle: 999,
} as const;

// Animation Timing
export const Animation = {
  duration: {
    micro: 100,
    short: 200,
    medium: 300,
    long: 500,
  },
  easing: {
    standard: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
    decelerate: 'cubic-bezier(0.0, 0.0, 0.2, 1)',
    accelerate: 'cubic-bezier(0.4, 0.0, 1, 1)',
  },
} as const;

// Layout Constants
export const Layout = {
  containerMaxWidth: {
    mobile: '100%',
    tablet: 768,
    desktop: 1200,
    ultrawide: 1400,
  },
  margins: {
    mobile: 20,
    desktop: 40,
  },
  breakpoints: {
    mobile: 0,
    tablet: 768,
    desktop: 1024,
    ultrawide: 1440,
  },
} as const;

// Shadow System - Minimal shadows for cutting-edge design
export const Shadow = {
  none: 'none',
  subtle: '0 2px 8px rgba(0, 0, 0, 0.04)',
  medium: '0 4px 16px rgba(0, 0, 0, 0.08)',
  strong: '0 8px 32px rgba(0, 0, 0, 0.12)',
} as const;

// Z-Index System
export const ZIndex = {
  base: 0,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal: 1040,
  popover: 1050,
  tooltip: 1060,
  toast: 1070,
} as const;

// Component Style Presets
export const ComponentStyles = {
  button: {
    primary: {
      borderRadius: BorderRadius.medium,
      fontWeight: Typography.fontWeight.medium,
      letterSpacing: Typography.letterSpacing.button,
    },
    secondary: {
      borderRadius: BorderRadius.medium,
      fontWeight: Typography.fontWeight.medium,
      letterSpacing: Typography.letterSpacing.button,
      borderWidth: 1,
    },
    ghost: {
      borderRadius: BorderRadius.medium,
      fontWeight: Typography.fontWeight.medium,
      letterSpacing: Typography.letterSpacing.button,
      backgroundColor: 'transparent',
    },
  },
  input: {
    default: {
      borderRadius: BorderRadius.medium,
      borderWidth: 1,
      fontSize: Typography.fontSize.body,
      fontWeight: Typography.fontWeight.regular,
    },
  },
  card: {
    default: {
      borderRadius: BorderRadius.medium,
      borderWidth: 1,
    },
  },
} as const;

// Accessibility
export const Accessibility = {
  minTouchTarget: 44,
  minContrastRatio: 4.5,
  focusRingWidth: 2,
  focusRingOffset: 2,
} as const;

// Grid System
export const Grid = {
  columns: 12,
  gutterWidth: Spacing.md,
  containerPadding: Spacing.lg,
} as const;

// Utility Functions
export const getSpacing = (multiplier: number): number => Spacing.xs * multiplier;

export const getFontSize = (size: keyof typeof Typography.fontSize): number => 
  Typography.fontSize[size];

export const getLineHeight = (size: keyof typeof Typography.lineHeight): number => 
  Typography.lineHeight[size];

export const getLetterSpacing = (size: keyof typeof Typography.letterSpacing): number => 
  Typography.letterSpacing[size];

// Component Helper Functions
export const getButtonSize = (size: 'small' | 'medium' | 'large') => 
  ComponentSize.button[size];

export const getIconSize = (size: 'small' | 'medium' | 'large' | 'xlarge'): number => 
  ComponentSize.icon[size];

// Responsive Helpers
export const isTablet = (width: number): boolean => 
  width >= Layout.breakpoints.tablet && width < Layout.breakpoints.desktop;

export const isDesktop = (width: number): boolean => 
  width >= Layout.breakpoints.desktop;

export const isMobile = (width: number): boolean => 
  width < Layout.breakpoints.tablet;

// Theme-aware helpers
export const getContainerPadding = (screenWidth: number): number => {
  if (isMobile(screenWidth)) {
    return Layout.margins.mobile;
  }
  return Layout.margins.desktop;
};

export const getMaxContainerWidth = (screenWidth: number): number | string => {
  if (isMobile(screenWidth)) {
    return Layout.containerMaxWidth.mobile;
  }
  if (isTablet(screenWidth)) {
    return Layout.containerMaxWidth.tablet;
  }
  if (screenWidth >= Layout.breakpoints.ultrawide) {
    return Layout.containerMaxWidth.ultrawide;
  }
  return Layout.containerMaxWidth.desktop;
};
