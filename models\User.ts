export interface User {
  id: string;
  email: string;
  username: string;
  displayName: string;
  avatar?: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  country?: string;
  timezone?: string;
  
  // Language & Accessibility
  primaryLanguage: string;
  secondaryLanguages: string[];
  autoTranslate: boolean;
  voiceEnabled: boolean;
  signLanguageEnabled: boolean;
  fontSize: 'small' | 'medium' | 'large';
  
  // Communication Preferences
  allowVoiceMessages: boolean;
  allowVideoMessages: boolean;
  readReceiptsEnabled: boolean;
  typingIndicatorsEnabled: boolean;
  
  // Privacy & Security
  profileVisibility: 'public' | 'friends' | 'private';
  lastSeenVisibility: 'everyone' | 'contacts' | 'nobody';
  allowContactByPhone: boolean;
  allowContactByEmail: boolean;
  twoFactorEnabled: boolean;
  
  // Status & Activity
  status: 'online' | 'away' | 'busy' | 'invisible';
  statusMessage?: string;
  lastSeen: Date;
  isVerified: boolean;
  accountType: 'free' | 'premium' | 'enterprise';
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export interface UserProfile {
  bio?: string;
  occupation?: string;
  interests: string[];
  socialLinks: {
    website?: string;
    linkedin?: string;
    twitter?: string;
  };
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
}

export interface UserPreferences {
  theme: 'white' | 'superBlack' | 'auto';
  notifications: {
    messages: boolean;
    mentions: boolean;
    groupInvites: boolean;
    friendRequests: boolean;
    systemUpdates: boolean;
    soundEnabled: boolean;
    vibrationEnabled: boolean;
    quietHours: {
      enabled: boolean;
      startTime: string;
      endTime: string;
    };
  };
  privacy: {
    blockUnknownUsers: boolean;
    requireApprovalForGroups: boolean;
    dataCollection: boolean;
    analyticsOptOut: boolean;
  };
  accessibility: {
    highContrast: boolean;
    reduceMotion: boolean;
    screenReaderOptimized: boolean;
    colorBlindSupport: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
  };
}