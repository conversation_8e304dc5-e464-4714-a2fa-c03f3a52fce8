# 🗣️ voXchat - Multilingual Chat Application

A modern, accessible multilingual chat application built with React Native/Expo and Python FastAPI, featuring real-time messaging, automatic translation, and voice capabilities.

## ✨ Features

### 🌍 **Multilingual Support**
- **Real-time Translation** - Automatic message translation with Google Translate API
- **Language Detection** - Smart language detection with confidence scoring
- **15+ Languages** - Support for major world languages
- **Translation Caching** - Optimized performance with cached translations

### 💬 **Real-time Messaging**
- **WebSocket Communication** - Live chat with instant message delivery
- **Typing Indicators** - See when someone is typing
- **Delivery Receipts** - Know when messages are delivered and read
- **Message History** - Persistent conversation history with MySQL
- **Connection Status** - Visual online/offline indicators

### 🎤 **Voice Features**
- **Voice Recording** - Record and send voice messages
- **Voice Playback** - Play voice messages with controls
- **Text-to-Speech** - Listen to messages in any language
- **Audio Permissions** - Proper permission handling across platforms

### ♿ **Accessibility**
- **Screen Reader Support** - Full accessibility compliance
- **Font Size Options** - Adjustable text sizes
- **Voice Navigation** - Voice command support
- **Sign Language Ready** - Prepared for sign language recognition

### 🗄️ **Database Support**
- **MySQL Integration** - Production-ready database with full schema
- **User Management** - Complete user profiles and preferences
- **Conversation Management** - Group and direct chat support
- **Message Reactions** - Emoji reactions and read receipts

## 🚀 Quick Start

### Firebase Setup (5 minutes)

```bash
# Clone the repository
git clone https://github.com/Raman247365/voXchat.git
cd voXchat

# Setup Firebase
setup-firebase.bat  # Windows
# OR
npm install firebase

# Configure Firebase (see FIREBASE_SETUP.md)
cp .env.example .env
# Edit .env with your Firebase credentials

# Start the app
npm install
npx expo start
```

**What you get:**
- Real-time messaging with Firestore
- User authentication with Firebase Auth
- Voice message storage with Firebase Storage
- Offline support and automatic sync
- Global CDN and automatic scaling

### Firebase Emulators (Development)

```bash
# Start Firebase emulators for local development
docker-compose -f docker-compose.firebase.yml up -d

# Access Firebase Emulator UI
# http://localhost:4000

# Start the app
npm install
npx expo start
```

**Development Services:**
- Firebase Emulator UI: http://localhost:4000
- Firestore Emulator: localhost:8080
- Auth Emulator: localhost:9099
- Storage Emulator: localhost:9199

## 📱 Platform Support

- **iOS** - Native iOS app via Expo
- **Android** - Native Android app via Expo  
- **Web** - Progressive Web App
- **Desktop** - Electron app (planned)

## 🛠️ Tech Stack

### Frontend
- **React Native** with Expo SDK 52
- **TypeScript** for type safety
- **Expo Router** for navigation
- **Expo AV** for voice recording/playback
- **WebSocket** for real-time communication

### Backend
- **Firebase** - Real-time NoSQL database, authentication, and storage
- **Firestore** - Real-time database with offline support
- **Firebase Auth** - User authentication and management
- **Firebase Storage** - Voice messages and media files
- **Google Translate API** - Translation services (optional)

### DevOps
- **Docker & Docker Compose** for easy deployment
- **GitHub Actions** for CI/CD (planned)
- **Automated Testing** with comprehensive test suites

## 📖 Documentation

- **[Firebase Setup Guide](FIREBASE_SETUP.md)** - Complete Firebase setup and configuration
- **[Quick Start Guide](QUICK_START.md)** - Get up and running quickly
- **[Development Guide](DEVELOPMENT.md)** - Development status and roadmap
- **[Firebase Console](https://console.firebase.google.com/)** - Manage your Firebase project

## 🧪 Testing

### Firebase Testing
```bash
# Test Firebase connection
npm run test:firebase

# Use Firebase Emulator UI
# http://localhost:4000
```

## 🔧 Configuration

### Environment Variables
```bash
# Firebase Configuration (Required)
EXPO_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
EXPO_PUBLIC_FIREBASE_APP_ID=your-app-id
EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id

# Optional: Enhanced Translation
GOOGLE_TRANSLATE_API_KEY=your-api-key
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Expo Team** for the amazing React Native framework
- **FastAPI** for the high-performance Python web framework
- **Google Translate** for translation services
- **MySQL** for reliable data persistence

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/Raman247365/voXchat/issues)
- **Discussions**: [GitHub Discussions](https://github.com/Raman247365/voXchat/discussions)
- **Email**: <EMAIL>

---

**Built with ❤️ for global communication and accessibility**
